import { PgDatabaseLayer } from '@rie/postgres-db';
import { PermissionsGroupsRepositoryLive } from '@rie/repositories';
import { PermissionsGroupsServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PermissionGroupServicesLayer = Layer.mergeAll(
  PermissionsGroupsRepositoryLive.Default,
  PermissionsGroupsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const PermissionGroupsRuntime = ManagedRuntime.make(
  PermissionGroupServicesLayer,
);
