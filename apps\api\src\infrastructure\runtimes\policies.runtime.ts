import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  UserPermissionsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

/**
 * Runtime for policy-related operations.
 * This provides all the dependencies needed for policy effects.
 */
const PoliciesServicesLayer = Layer.mergeAll(
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const PoliciesRuntime = ManagedRuntime.make(PoliciesServicesLayer);
