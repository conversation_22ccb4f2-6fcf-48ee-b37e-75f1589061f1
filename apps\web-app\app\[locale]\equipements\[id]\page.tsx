import Details from '@/app/[locale]/equipements/[id]/details';
import { getQueryClientOptions } from '@/constants/query-client';
import { equipmentByIdOptions } from '@/hooks/equipment/useGetEquipmentById';
import { mapEquipmentToDetails } from '@/services/mappers/equipment';
import type { PageDetailsParams } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function EquipmentDescription(props: PageDetailsParams) {
  const params = await props.params;

  const { id, locale } = params;

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  await queryClient.prefetchQuery(
    equipmentByIdOptions(id, locale, {
      select: (data) => mapEquipmentToDetails(data.equipment),
    }),
  );
  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <div className="w-full">
        <Details />
      </div>
    </HydrationBoundary>
  );
}
