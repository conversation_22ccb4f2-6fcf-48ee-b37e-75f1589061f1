import { getEquipmentsCount } from '@/services/equipments.service';
import { getInfrastructuresCount } from '@/services/infrastructures.service';
import { queryOptions } from '@tanstack/react-query';

export const infrastructuresCountOptions = queryOptions<number>({
  queryKey: ['v2', 'infrastructures', 'count'],
  queryFn: () => getInfrastructuresCount(),
});

export const equipmentsCountOptions = queryOptions<number>({
  queryKey: ['v2', 'equipments', 'count'],
  queryFn: () => getEquipmentsCount(),
});
