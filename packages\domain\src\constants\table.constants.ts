import type { CollectionViewType, ResourceViewType } from '../types';

export const PEOPLE_COLLECTION_VIEW_COLUMNS: Record<
  CollectionViewType,
  Record<string, boolean>
> = {
  list: {
    uid: true,
    familyName: true,
    givenName: true,
  },
  grid: {
    uid: true,
    familyName: true,
    givenName: true,
  },
  select: {
    id: true,
    familyName: true,
    givenName: true,
  },
} as const;

export const PEOPLE_RESOURCE_VIEW_COLUMNS: Record<
  ResourceViewType,
  Record<string, boolean>
> = {
  detail: {
    uid: true,
    familyName: true,
    givenName: true,
    emails: true,
  },
  edit: {
    uid: true,
    familyName: true,
    givenName: true,
    emails: true,
  },
} as const;

export const UNIT_COLLECTION_VIEW_COLUMNS: Record<
  CollectionViewType,
  Record<string, boolean>
> = {
  list: {
    uid: true,
    name: true,
    acronym: true,
    details: true,
    // other list columns
  },
  grid: {
    uid: true,
    name: true,
    acronym: true,
    details: true,
  },
  select: {
    id: true,
    name: true,
    locale: true,
  },
} as const;

export const UNIT_RESOURCE_VIEW_COLUMNS: Record<
  ResourceViewType,
  Record<string, boolean>
> = {
  detail: {
    uid: true,
    name: true,
    acronym: true,
    details: true,
  },
  edit: {
    uid: true,
    name: true,
    acronym: true,
    details: true,
  },
} as const;
