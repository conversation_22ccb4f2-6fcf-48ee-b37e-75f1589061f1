import { PermissionGate } from '@/components/permissions/permission-gate';
import type { SessionData } from '@/lib/better-auth';
import { render } from '@/test/render';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the useSession hook
vi.mock('@/lib/better-auth', () => ({
  useSession: vi.fn(),
}));

import { useSession } from '@/lib/better-auth';

// Mock refetch function
const mockRefetch = vi.fn();

// Mock session data
const adminSessionMock: SessionData = {
  user: {
    id: 'bea48f37-6c6d-11ee-b96a-0242c0a8f30c',
    name: 'Admin Demo',
    email: '<EMAIL>',
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    image: null,
  },
  session: {
    id: 'session-id',
    token: 'session-token',
    userId: 'bea48f37-6c6d-11ee-b96a-0242c0a8f30c',
    expiresAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    ipAddress: null,
    userAgent: null,
  },
  roles: ['SystemAdmin'],
  permissions: {
    globalPermissions: [
      { domain: 'equipment', action: 'update' },
      { domain: 'infrastructure', action: 'update' },
    ],
    address: [],
    applicationSector: [],
    building: [],
    campus: [],
    equipment: [],
    excellenceHub: [],
    fundingProject: [],
    infrastructure: [],
    innovationLab: [],
    institution: [],
    media: [],
    people: [],
    researchField: [],
    room: [],
    serviceContract: [],
    serviceOffer: [],
    technique: [],
    unit: [],
    user: [],
    userRole: [],
    vendor: [],
    visibility: [],
    documentationCategory: [],
    equipmentCategory: [],
    equipmentStatus: [],
    equipmentType: [],
    fundingProjectType: [],
    fundingProjectIdentifierType: [],
    infrastructureStatus: [],
    infrastructureType: [],
    institutionType: [],
    mediaType: [],
    peopleRoleType: [],
    roomCategory: [],
    unitType: [],
  },
};

const userSessionMock: SessionData = {
  user: {
    id: 'bea48e1a-6c6d-11ee-b96a-0242c0a8f30c',
    name: 'User Demo',
    email: '<EMAIL>',
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    image: null,
  },
  session: {
    id: 'session-id-2',
    token: 'session-token-2',
    userId: 'bea48e1a-6c6d-11ee-b96a-0242c0a8f30c',
    expiresAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    ipAddress: null,
    userAgent: null,
  },
  roles: ['ROLE_USER'],
  permissions: {
    globalPermissions: [],
    address: [],
    applicationSector: [],
    building: [],
    campus: [],
    equipment: [],
    excellenceHub: [],
    fundingProject: [],
    infrastructure: [],
    innovationLab: [],
    institution: [],
    media: [],
    people: [],
    researchField: [],
    room: [],
    serviceContract: [],
    serviceOffer: [],
    technique: [],
    unit: [],
    user: [],
    userRole: [],
    vendor: [],
    visibility: [],
    documentationCategory: [],
    equipmentCategory: [],
    equipmentStatus: [],
    equipmentType: [],
    fundingProjectType: [],
    fundingProjectIdentifierType: [],
    infrastructureStatus: [],
    infrastructureType: [],
    institutionType: [],
    mediaType: [],
    peopleRoleType: [],
    roomCategory: [],
    unitType: [],
  },
};

const infrastructureManagerSessionMock: SessionData = {
  user: {
    id: 'bea48f8b-6c6d-11ee-b96a-0242c0a8f30c',
    name: 'Infrastructure Manager Demo',
    email: '<EMAIL>',
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    image: null,
  },
  session: {
    id: 'session-id-3',
    token: 'session-token-3',
    userId: 'bea48f8b-6c6d-11ee-b96a-0242c0a8f30c',
    expiresAt: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    ipAddress: null,
    userAgent: null,
  },
  roles: ['ROLE_INFRASTRUCTURE_MANAGER'],
  permissions: {
    globalPermissions: [
      { domain: 'equipment', action: 'update' },
      { domain: 'infrastructure', action: 'update' },
    ],
    equipment: ['E000000875', 'E000000876'],
    infrastructure: ['I000033', 'I000388'],
    address: [],
    applicationSector: [],
    building: [],
    campus: [],
    excellenceHub: [],
    fundingProject: [],
    innovationLab: [],
    institution: [],
    media: [],
    people: [],
    researchField: [],
    room: [],
    serviceContract: [],
    serviceOffer: [],
    technique: [],
    unit: [],
    user: [],
    userRole: [],
    vendor: [],
    visibility: [],
    documentationCategory: [],
    equipmentCategory: [],
    equipmentStatus: [],
    equipmentType: [],
    fundingProjectType: [],
    fundingProjectIdentifierType: [],
    infrastructureStatus: [],
    infrastructureType: [],
    institutionType: [],
    mediaType: [],
    peopleRoleType: [],
    roomCategory: [],
    unitType: [],
  },
};

describe('<PermissionGate />', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render children when the user is admin', () => {
    vi.mocked(useSession).mockReturnValue({
      data: adminSessionMock,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate resourceType="equipment" action="read">
        <p>Child component</p>
      </PermissionGate>,
    );
    expect(screen.getByText('Child component')).toBeInTheDocument();
  });

  it('should render children when the user has role Editor and wants to create an equipment', () => {
    vi.mocked(useSession).mockReturnValue({
      data: userSessionMock,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate action="create" resourceType="equipment">
        <p>Child component</p>
      </PermissionGate>,
    );
    expect(screen.queryByText('Child component')).not.toBeInTheDocument();
  });

  it('should render children when the user has role SuperEditor and wants to create an infrastructure', () => {
    vi.mocked(useSession).mockReturnValue({
      data: userSessionMock,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate action="create" resourceType="infrastructure">
        <p>Child component</p>
      </PermissionGate>,
    );
    expect(screen.queryByText('Child component')).not.toBeInTheDocument();
  });

  it('should not render children when the user is not logged in', () => {
    vi.mocked(useSession).mockReturnValue({
      data: null,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate action="read" resourceType="equipment">
        <p>Child component</p>
      </PermissionGate>,
    );
    expect(screen.queryByText('Child component')).not.toBeInTheDocument();
  });

  it('should not render children when the user has role User and wants to create an equipment', () => {
    vi.mocked(useSession).mockReturnValue({
      data: userSessionMock,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate action="create" resourceType="equipment">
        <p>Child component</p>
      </PermissionGate>,
    );
    expect(screen.queryByText('Child component')).not.toBeInTheDocument();
  });
  it('should not render children when the user has role Editor and wants to delete an infrastructure', () => {
    vi.mocked(useSession).mockReturnValue({
      data: userSessionMock,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate action="delete" resourceType="infrastructure">
        <p>Child component</p>
      </PermissionGate>,
    );
    expect(screen.queryByText('Child component')).not.toBeInTheDocument();
  });
  it("should render children when the user has role Infrastructure Manager and wants to update an equipment that's associated with one of the infrastructures he's responsible", async () => {
    vi.mocked(useSession).mockReturnValue({
      data: infrastructureManagerSessionMock,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate
        resourceId={'E000000875'}
        action="update"
        resourceType="equipment"
      >
        <p>Child component</p>
      </PermissionGate>,
    );

    expect(await screen.findByText('Child component')).toBeInTheDocument();
  });

  it("should NOT render children when the user has role Infrastructure Manager and wants to update an equipment that's not associated with one of the infrastructures he's responsible", async () => {
    vi.mocked(useSession).mockReturnValue({
      data: infrastructureManagerSessionMock,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate
        resourceId={'T000000875'}
        action="update"
        resourceType="equipment"
      >
        <p>Child component</p>
      </PermissionGate>,
    );

    expect(screen.queryByText('Child component')).not.toBeInTheDocument();
  });

  it("should render children when the user has role Infrastructure Manager and wants to update an infrastructures he's responsible", async () => {
    vi.mocked(useSession).mockReturnValue({
      data: infrastructureManagerSessionMock,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate
        resourceId={'I000033'}
        action="update"
        resourceType="infrastructure"
      >
        <p>Child component</p>
      </PermissionGate>,
    );

    expect(await screen.findByText('Child component')).toBeInTheDocument();
  });

  it("should NOT render children when the user has role Infrastructure Manager and wants to update an infrastructures he isn't responsible", async () => {
    vi.mocked(useSession).mockReturnValue({
      data: infrastructureManagerSessionMock,
      isPending: false,
      error: null,
      refetch: mockRefetch,
    });

    render(
      <PermissionGate
        resourceId={'T000033'}
        action="update"
        resourceType="infrastructure"
      >
        <p>Child component</p>
      </PermissionGate>,
    );

    expect(screen.queryByText('Child component')).not.toBeInTheDocument();
  });
});
