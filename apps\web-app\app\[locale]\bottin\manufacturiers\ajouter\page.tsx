import { AddManufacturer } from '@/app/[locale]/bottin/manufacturiers/ajouter/add-manufacturer';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { manufacturerFormSections } from '@/constants/bottin/manufacturer';
import { getQueryClientOptions } from '@/constants/query-client';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { BasePageParams, ControlledListKey } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function NewManufacturerPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  const formSections = await getFormSections({
    resourceName: 'manufacturers',
    sections: manufacturerFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = ['local', 'building'];

  const t0 = performance.now();

  await Promise.all(
    controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  );

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "local" and "building" took ${t1 - t0} milliseconds.`,
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <AddManufacturer locale={locale} formSections={formSections} />
    </HydrationBoundary>
  );
}
