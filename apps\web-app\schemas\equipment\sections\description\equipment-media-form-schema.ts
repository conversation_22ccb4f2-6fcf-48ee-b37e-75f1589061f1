import {
  FILE_MAX_UPLOAD_SIZE,
  IMAGE_MAX_UPLOAD_SIZE,
} from '@/constants/equipments';
import { z } from 'zod';

export const getEquipmentMediaFormSchema = (
  t: (val: string, args?: Record<string, number | string>) => string,
) => {
  const imageFileSchema = z
    .instanceof(File)
    .optional()
    .superRefine((file, ctx) => {
      if (file && file.size > IMAGE_MAX_UPLOAD_SIZE) {
        ctx.addIssue({
          code: z.ZodIssueCode.too_big,
          inclusive: true,
          maximum: IMAGE_MAX_UPLOAD_SIZE,
          message: t('description.media.fields.images.error.maxFileSize', {
            size: IMAGE_MAX_UPLOAD_SIZE,
          }),
          type: 'number',
        });
      }
    });

  const urlSchema = z.string().url().optional();

  const imageSchema = z.array(
    z.discriminatedUnion('type', [
      z.object({
        data: imageFileSchema,
        id: z.string().optional(),
        type: z.literal('file'),
      }),
      z.object({
        data: urlSchema,
        id: z.string().optional(),
        type: z.literal('url'),
      }),
    ]),
  );

  const videoSchema = z.array(
    z.discriminatedUnion('type', [
      z.object({
        data: urlSchema,
        id: z.string().optional(),
        type: z.literal('url'),
      }),
    ]),
  );

  const documentFileSchema = z
    .instanceof(File)
    .optional()
    .superRefine((file, ctx) => {
      if (file && file.size > FILE_MAX_UPLOAD_SIZE) {
        ctx.addIssue({
          code: z.ZodIssueCode.too_big,
          inclusive: true,
          maximum: FILE_MAX_UPLOAD_SIZE,
          message: t('description.media.fields.documents.error.maxFileSize', {
            size: FILE_MAX_UPLOAD_SIZE,
          }),
          type: 'number',
        });
      }
    });

  const commonDocumentsFields = z.object({
    category: z.object({
      label: z.string().trim(),
      value: z.string().trim(),
    }),
    descriptions: z.array(
      z.object({
        locale: z.string().trim(),
        value: z
          .string()
          .trim()
          .max(1500, {
            message: t('description.media.fields.descriptions.error.max', {
              max: 1500,
            }),
          }),
      }),
    ),
    isConfidential: z.boolean().default(false),
  });

  const documentSchema = z.array(
    z.discriminatedUnion('type', [
      z.object({
        data: documentFileSchema,
        id: z.string().optional(),
        type: z.literal('file'),
        ...commonDocumentsFields.shape,
      }),
      z.object({
        data: urlSchema,
        id: z.string().optional(),
        type: z.literal('url'),
        ...commonDocumentsFields.shape,
      }),
    ]),
  );

  return z.object({
    documents: documentSchema,
    images: imageSchema,
    videos: videoSchema,
  });
};
