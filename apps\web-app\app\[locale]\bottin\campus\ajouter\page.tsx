import { AddCampus } from '@/app/[locale]/bottin/campus/ajouter/add-campus';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { campusFormSections } from '@/constants/bottin/campus';
import { getQueryClientOptions } from '@/constants/query-client';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { BasePageParams, ControlledListKey } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';

export default async function NewCampusPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  const formSections = await getFormSections({
    resourceName: 'directory',
    sections: campusFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = ['organisation'];

  const t0 = performance.now();

  await Promise.all(
    controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  );

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "organization" took ${t1 - t0} milliseconds.`,
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <AddCampus locale={locale} formSections={formSections} />
    </HydrationBoundary>
  );
}
