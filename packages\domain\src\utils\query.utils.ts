import {
  DEFAULT_PAGINATION_LIMIT,
  DEFAULT_PAGINATION_PAGE,
  DEFAULT_SORT_COLUMN,
  DEFAULT_SORT_ORDER,
} from '@rie/constants';
import * as Option from 'effect/Option';
import type {
  CollectionQuerySchemaType,
  Pagination,
  PaginationOptions,
  PaginationParams,
  QueryConfig,
  Sort,
  SortOptions,
  SortableField,
} from '../types';

/**
 * Creates a sort config
 * @param column
 * @param order
 * @returns SortOptions
 */
export const createSortConfig = <const T extends SortableField>({
  column,
  order,
}: Partial<SortOptions<T>> = {}) => {
  const columnName = column ? column : DEFAULT_SORT_COLUMN;
  const orderValue = order ? order : DEFAULT_SORT_ORDER;

  return { column: columnName, order: orderValue } as const;
};

/**
 * Checks whether the column parameter is valid
 * Must be a string starting and ending with brackets
 * @param column
 * @returns boolean
 */
const isValidColumn = (column: string) => {
  if (column.startsWith('[') && column.endsWith(']')) {
    return true;
  }

  if (column.replace('[', '').replace(']', '').trim() !== '') {
    return true;
  }

  return false;
};

/**
 * Creates a sort config from query params
 * @param sort
 * @returns SortOptions
 */
export const getSortConfigFromQueryParams = (sort?: string) => {
  if (!sort) {
    return createSortConfig();
  }
  const [possibleColumn, possibleOrder] = sort.split('=');

  if (!possibleColumn || !possibleOrder || !isValidColumn(possibleColumn)) {
    return createSortConfig();
  }

  const column = possibleColumn.replace('[', '').replace(']', '');
  const order = possibleOrder as Sort;

  return createSortConfig({ column, order });
};

/**
 * Creates a pagination config from pagination options
 * Handles the special case where limit === -1 (no offset needed)
 * @param page
 * @param limit
 * @returns Pagination
 */
export const createPaginationConfig = ({
  page = DEFAULT_PAGINATION_PAGE,
  limit = DEFAULT_PAGINATION_LIMIT,
}: Partial<PaginationOptions> = {}): Pagination => {
  if (limit === -1) {
    return { limit: -1, offset: 0 };
  }

  return {
    limit,
    offset: (page - 1) * limit,
  };
};

/**
 * Gets pagination config from query params
 * Returns limit if limit is -1 (no pagination needed)
 * @param page
 * @param limit
 * @returns PaginationOptions | Pick<PaginationOptions, 'limit'>
 */
export const getPaginationConfigFromQueryParams = ({
  page,
  limit,
}: Partial<PaginationOptions> = {}):
  | PaginationOptions
  | Pick<PaginationOptions, 'limit'> => {
  if (limit === -1) {
    return { limit };
  }
  return {
    page: page ?? DEFAULT_PAGINATION_PAGE,
    limit: limit ?? DEFAULT_PAGINATION_LIMIT,
  };
};

/**
 * Gets offset from pagination config
 * Returns None if limit is -1 (no offset needed)
 * Returns Some(number) with calculated offset otherwise
 * @param page
 * @param limit
 * @returns Option<number>
 */
export const getOffsetFromPagination = ({
  page = DEFAULT_PAGINATION_PAGE,
  limit = DEFAULT_PAGINATION_LIMIT,
}: Partial<PaginationOptions> = {}): Option.Option<number> => {
  if (limit === -1) {
    return Option.none();
  }

  return Option.some((page - 1) * limit);
};

/**
 * Builds a query config from query params
 * @param sort string | undefined
 * @param page number | undefined
 * @param limit number | undefined
 * @param view 'list' | 'select' | undefined
 * @param locale 'fr' | 'en' | undefined
 * @returns QueryConfig
 */
export const buildQueryConfig = ({
  sort,
  page,
  limit,
  locale,
  view,
}: CollectionQuerySchemaType): QueryConfig => {
  const sortConfig = getSortConfigFromQueryParams(sort);
  const paginationConfig = createPaginationConfig({ page, limit });

  return {
    sort: sortConfig,
    locale,
    view,
    pagination: Option.some(paginationConfig),
  };
};

/**
 * Convertit les options de pagination en paramètres de pagination pour PgDatabase
 * Gère le cas limite où limit === -1 (pas d'offset nécessaire)
 */
export const toPaginationParams = (
  options: Partial<PaginationOptions> = {},
): PaginationParams => {
  const { limit, offset } = createPaginationConfig(options);
  return { limit, offset };
};

/**
 * Convertit QueryConfig en PaginationParams
 * Note: Depuis que buildQueryConfig retourne toujours Option.some() pour pagination,
 * cette fonction pourrait être simplifiée à juste extraire la valeur de l'Option
 */
export const paginationConfigToPaginationParams = (
  queryConfig: QueryConfig,
): PaginationParams => {
  const pagination = Option.getOrThrow(queryConfig.pagination);

  return {
    limit: pagination.limit,
    offset: pagination.offset,
  };
};
