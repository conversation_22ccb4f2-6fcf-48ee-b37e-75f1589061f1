'use client';

import { useSession } from '@/lib/better-auth';
import type { UserPermissionStatus } from '@/types/permission.type';
import { ADMIN_ROLE } from '@rie/constants';
import type {
  PermissionAction,
  ResourceType,
  SessionData,
} from '@rie/domain/types';
import { useEffect, useState } from 'react';

type HasPermissionToArgs = {
  action: PermissionAction;
  resourceType: ResourceType;
  resourceId?: string;
};

// Admin roles that grant all permissions

const checkUserHasPermission = ({
  sessionData,
  resourceType,
  action,
  resourceId,
}: {
  sessionData: SessionData;
} & HasPermissionToArgs): UserPermissionStatus => {
  // Check if user has admin role
  if (sessionData.roles.some((role) => role === ADMIN_ROLE)) {
    return 'granted';
  }

  // Check if user has the generic permission (e.g., 'equipment:read')
  const hasGenericPermission = sessionData.permissions.globalPermissions.some(
    (p) => p.domain === resourceType && p.action === action,
  );

  // If the user doesn't have the generic permission, access is denied.
  if (!hasGenericPermission) {
    return 'denied';
  }

  // If they have the generic permission and no specific resource is being checked, access is granted.
  if (!resourceId) {
    return 'granted';
  }

  // If a specific resource is being checked, we must also verify resource-level access.
  // Access the resource-specific permissions directly from the permissions object.
  const resourceAccessList = sessionData.permissions[resourceType];

  // Check if a list of accessible IDs exists for this resource type and if the user has access.
  if (
    Array.isArray(resourceAccessList) &&
    resourceAccessList.includes(resourceId)
  ) {
    return 'granted'; // Has generic permission AND resource-specific access
  }

  // The user has the generic permission but not access to this specific resource.
  return 'denied';
};

export const useUserHasPermissionTo = ({
  resourceId,
  resourceType,
  action,
}: HasPermissionToArgs): {
  isLoading: boolean;
  permissionStatus: UserPermissionStatus;
} => {
  const [permissionStatus, setPermissionStatus] =
    useState<UserPermissionStatus>('unknown');

  const { data: session, isPending: isLoading } = useSession();

  useEffect(() => {
    if (isLoading) {
      setPermissionStatus('unknown');
      return;
    }

    if (!session) {
      setPermissionStatus('denied');
      return;
    }

    const status = checkUserHasPermission({
      // Unfortunately, because `permissions` in session is typed as
      // import("/packages/services/build/dts/index").AccessTree
      // Typescript isn't able to resolve it and the type is any, so we have to cast it
      sessionData: session as SessionData,
      resourceType,
      action,
      resourceId,
    });
    setPermissionStatus(status);
  }, [session, isLoading, resourceType, action, resourceId]);

  return { isLoading, permissionStatus };
};
