'use client';

import { buildingsColumns } from '@/app/[locale]/bottin/batiments/columns';
import { GenericList } from '@/components/bottin-table/generic-list';
import { initialColumnVisibility } from '@/constants/buildings';
import type { SupportedLocale } from '@/types/locale';
import type { BuildingList } from '@rie/domain/types';

type BuildingsListProps = {
  locale: SupportedLocale;
};

export const BuildingsList = ({ locale }: BuildingsListProps) => {
  return (
    <GenericList<'building', 'list', BuildingList, Record<string, unknown>>
      controlledListKey="building"
      view="list"
      locale={locale}
      columns={buildingsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="buildings"
    />
  );
};
