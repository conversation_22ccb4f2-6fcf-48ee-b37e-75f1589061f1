'use client';

import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { HeaderRenderer } from '@/components/header-renderer/header-renderer';
import { useDeleteDirectoryEntity } from '@/hooks/bottin/use-delete-directory-entity';
import { extractDirectoryEditPathnames } from '@/i18n/i18n.helpers';
import { pathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import type { SupportedLocale } from '@/types/locale';
import { Button } from '@/ui/button';
import type { PersonList } from '@rie/domain/types';
import type { ColumnDef, Row } from '@tanstack/react-table';
import { useFormatter, useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

export const peopleColumns = (
  _locale: SupportedLocale,
): ColumnDef<PersonList>[] => {
  const directoryEntity = 'people' as const;

  return [
    {
      accessorKey: 'lastName',
      cell: (cell) => cell.getValue(),
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastName"
        />
      ),
      id: 'lastName',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'firstName',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.firstName"
        />
      ),
      id: 'firstName',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'email',
      cell: ({ cell }) => {
        const emails = cell.getValue() as string[];
        return emails && emails.length > 0 ? emails[0] : '-';
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.email"
        />
      ),
      id: 'email',
      maxSize: 300,
      minSize: 200,
    },
    {
      accessorKey: 'lastUpdatedAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'lastUpdatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => <PersonActions row={row} />,
      id: 'actions',
      size: 105,
    },
  ];
};

const PersonActions = ({ row }: { row: Row<PersonList> }) => {
  const tCommon = useTranslations('common');
  const { mutate: deleteDirectoryEntity } = useDeleteDirectoryEntity('people');
  const directoryEditPathnames = extractDirectoryEditPathnames(pathnames);
  const pathname = directoryEditPathnames.people;

  return (
    <div className="flex items-center justify-center gap-x-3">
      <Button asChild size="icon">
        <Link
          data-testid="edit-person"
          href={{
            params: { id: row.original.id },
            pathname,
          }}
        >
          <FaEdit className="h-4 w-4" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={() => {
          deleteDirectoryEntity(row.original.id);
        }}
        title={tCommon('deleteItemQuestion', {
          item: `${row.original.firstName} ${row.original.lastName}`,
        })}
        trigger={
          <Button size="icon" variant="destructive" data-testid="delete-person">
            <MdDeleteForever className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
};
