import { MediaSourceItem } from '@/app/[locale]/equipements/form/components/media-source/media-source-item';
import { getDefaultMediaValue } from '@/app/[locale]/equipements/form/components/media-source/media-source.helpers';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import type { MediaSourceType } from '@/types/media';
import { Button } from '@/ui/button';
import { Heading } from '@/ui/heading';
import { useTranslations } from 'next-intl';
import { Fragment } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { FiPlus, FiTrash2 } from 'react-icons/fi';

type MediaSourceProps = {
  fileSize: number;
  source: MediaSourceType;
  title: string;
};

export const MediaSource = ({ source, title }: MediaSourceProps) => {
  const tCommon = useTranslations('common');
  const { control } = useFormContext<EquipmentFormSchema>();
  const { append, fields, remove } = useFieldArray({ control, name: source });
  const locale = useAvailableLocale();

  return (
    <div className="grid gap-y-4 py-6">
      <Heading level={5}>{title}</Heading>
      {fields.map((field, index) => (
        <Fragment key={field.id}>
          <MediaSourceItem
            control={control}
            fieldName={`${source}.${index}`}
            source={source}
          />
          <Button
            className="justify-self-end"
            onClick={() => remove(index)}
            type="button"
            variant="destructive"
          >
            <FiTrash2 className="mr-2 h-4 w-4" />
            {tCommon('deleteResource', {
              resource: tCommon(source.slice(0, -1)),
            })}
          </Button>
        </Fragment>
      ))}
      <Button
        className="justify-self-end"
        onClick={() => append(getDefaultMediaValue(source, locale))}
        type="button"
      >
        <FiPlus className="mr-2 h-4 w-4" />{' '}
        {tCommon('addResource', { resource: tCommon(source.slice(0, -1)) })}
      </Button>
    </div>
  );
};
