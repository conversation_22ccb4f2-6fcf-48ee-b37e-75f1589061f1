import { FieldInfo } from '@/components/FieldInfo';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import type { CalendarProps } from '@/ui/calendar';
import { DatePicker } from '@/ui/datepicker';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { type DateTimeFormatOptions, useTranslations } from 'next-intl';
import { type FieldValues, type Path, useFormContext } from 'react-hook-form';

type DatePickerFieldProps<TFieldData extends FieldValues> = {
  className?: string;
  fieldLabel: string;
  fieldName: Path<TFieldData>;
  formatOptions?: DateTimeFormatOptions;
  placeholder?: string;
  required?: boolean;
  tooltip?: string;
} & Omit<CalendarProps, 'mode' | 'selected'>;

export const DatePickerField = <TFieldData extends FieldValues>({
  className,
  fieldLabel,
  fieldName,
  formatOptions,
  required,
  tooltip,
  ...calendarProps
}: DatePickerFieldProps<TFieldData>) => {
  const tCommon = useTranslations('common');
  const { control } = useFormContext<TFieldData>();

  return (
    <FormField
      control={control}
      name={fieldName}
      render={({ field }) => {
        return (
          <FormItem className={className}>
            <LabelTooltip
              htmlFor={fieldName}
              label={fieldLabel}
              required={required}
              tooltip={tooltip}
            />
            <FormControl>
              <DatePicker
                className="w-full"
                formatOptions={formatOptions}
                mode="single"
                onSelect={field.onChange}
                placeholder={tCommon('pickDate')}
                selected={field.value ? new Date(field.value) : undefined}
                {...calendarProps}
              />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        );
      }}
    />
  );
};
