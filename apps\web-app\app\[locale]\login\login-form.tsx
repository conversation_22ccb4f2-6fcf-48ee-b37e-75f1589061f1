'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { getRedirectPath } from '@/helpers/get-redirect-path';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import type { SideMenuPathnames } from '@/i18n/settings';
import { authClient } from '@/lib/better-auth';
import { useRouter } from '@/lib/navigation';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { type ComponentProps, type FormEvent, useState } from 'react';

export function LoginForm({ className, ...props }: ComponentProps<'div'>) {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const locale = useAvailableLocale();
  const searchParams = useSearchParams();
  const router = useRouter();

  const fromUrl = searchParams.get('from');
  const redirectPath = fromUrl
    ? getRedirectPath(fromUrl, locale)
    : { pathname: '/' as SideMenuPathnames };

  const signIn = async (e: FormEvent) => {
    e.preventDefault();
    await authClient.signIn.email(
      {
        email,
        password,
      },
      {
        onRequest: () => {
          setLoading(true);
        },
        onSuccess: () => {
          console.log('Login success!', JSON.stringify(redirectPath, null, 2));
          // @ts-expect-error -- TypeScript will validate that only known `params`
          // are used in combination with a given `pathname`. Since the redirect path
          // comes from getRedirectPath which ensures correct param structure, we can skip runtime checks.
          router.push(redirectPath);
        },
        onError: (ctx) => {
          setLoading(false);
          alert(ctx.error.message);
        },
      },
    );
  };

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle>Login to your account</CardTitle>
          <CardDescription>
            Enter your email below to login to your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form>
            <div className="flex flex-col gap-6">
              <div className="grid gap-3">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="grid gap-3">
                <div className="flex items-center">
                  <Label htmlFor="password">Password</Label>
                  <a
                    // biome-ignore lint/a11y/useValidAnchor: <explanation>
                    href="#"
                    className="ml-auto inline-block text-sm underline-offset-4 hover:underline"
                  >
                    Forgot your password?
                  </a>
                </div>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
              <div className="flex flex-col gap-3">
                <Button className="w-full" disabled={loading} onClick={signIn}>
                  Sign in
                </Button>
                <Button
                  onClick={async () => {
                    try {
                      await authClient.signIn.social({
                        provider: 'github',
                        callbackURL: `http://localhost:3000/rie/${locale}`,
                      });
                    } catch (error) {
                      console.error('GitHub sign in error:', error);
                    }
                  }}
                  variant="outline"
                  className="w-full"
                  type="button"
                >
                  Sign in with GitHub
                </Button>
              </div>
            </div>
            <div className="mt-4 text-center text-sm">
              Don&apos;t have an account?{' '}
              <Link href="/signup" className="underline underline-offset-4">
                Sign up
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
