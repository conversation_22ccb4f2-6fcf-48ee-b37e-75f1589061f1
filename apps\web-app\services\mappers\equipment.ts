import { mapFacetFullToFacet } from '@/services/mappers/map-facets-to-filters';
import type { ApiReturnType, MapperReturnType } from '@/types/common';
import type {
  DocumentType,
  EquipmentDescriptionDetailed,
  EquipmentDescriptionHeader,
  EquipmentDescriptionMaintenance,
  EquipmentDescriptionRequirements,
  EquipmentFull,
  EquipmentStructure,
  MediaType,
} from '@/types/equipment';
import type { FacetValue } from '@/types/filters';
import type { ResourceStatusType } from '@/types/infrastructure';

export const mapEquipmentToTableColumns = ({
  count,
  data,
  facets,
}: ApiReturnType<EquipmentFull[]>): MapperReturnType<
  EquipmentStructure[],
  FacetValue[]
> => {
  return {
    count,
    data: data.map((equipment) => ({
      decommissionDate: equipment.dateDecommission,
      equipmentCategories: equipment.categories?.map(
        (category) => category.text,
      ),
      equipmentName: equipment.nom,
      equipmentType: equipment.typeEquipement.noms.fr,
      id: equipment.id,
      infrastructureId: equipment.infrastructure.id,
      installationDate: equipment.dateInstalOuAchat,
      location: equipment.emplacement?.text,
      manufacturer: equipment.manufacturier?.text,
      model: equipment.modele || null,
      name: equipment.text,
      parentInfrastructure: equipment.infrastructure.text,
      purchaseDate: equipment.dateAchat,
      status: equipment.etatEquipement.uid as ResourceStatusType,
      statusText: equipment.etatEquipement.noms.fr,
      // technicalManager: equipment.responsablesTechnique.map(
      //   (manager) => manager.text
      // ), //TODO: A Fix
      technicalManager: [], //TODO: Comprendre pourquoi il n'y a pas de responsable technique
      updatedAt: equipment.lastUpdatedAt,
    })),
    facets: mapFacetFullToFacet(facets),
  };
};

export function mapEquipmentToDescriptionDetails(
  equipment: EquipmentFull,
): EquipmentDescriptionHeader {
  return {
    categories: equipment.categories?.map((category) => category.text) || [],
    infrastructureId: equipment.infrastructure.id,
    infrastructureName: equipment.infrastructure.nom,
    nom: equipment.nom,
    status: equipment.etatEquipement.uid as ResourceStatusType,
    statusText: equipment.etatEquipement.noms?.fr,
  };
}

export const mapEquipmentToMaintenanceDetails = (
  equipment: EquipmentFull,
): EquipmentDescriptionMaintenance => {
  return {
    authorizedRepairers: equipment.reparateurs.map((repairer) => repairer.text),
    financingSource: equipment.financement,
    maintenance: equipment.maintenance,
    maintenanceInterval: [
      equipment.frequenceMaintenance?.valeur,
      equipment.frequenceMaintenance?.unite.text,
    ].join(' '),
  };
};

export const mapEquipmentToRequirementsDetails = (
  equipment: EquipmentFull,
): EquipmentDescriptionRequirements => {
  return {
    depth: equipment.equipementDimension?.profondeur,
    height: equipment.equipementDimension?.hauteur,
    weight: equipment.equipementDimension?.masse,
    width: equipment.equipementDimension?.largeur,
  };
};

export const mapEquipmentToDescription = (
  equipment: EquipmentFull,
): EquipmentDescriptionDetailed => {
  return {
    accessories: equipment.equipementAccessoires.map(
      (accessory) => accessory.text,
    ),
    comments: equipment.commentaire,
    components: equipment.equipementComposantes.map(
      (component) => component.text,
    ),
    decommissionDate: equipment.dateDecommission
      ? new Date(equipment.dateDecommission).toLocaleDateString('fr-CA')
      : null, // Format de date normal ou null
    detailerWebsites: equipment.detaillants.map((detailer) => detailer.url),
    disposalTerms: equipment.disposition,
    estimatedLifetime:
      equipment.equipementDureeVie?.valeur &&
      equipment.equipementDureeVie?.unite?.text
        ? [
            equipment.equipementDureeVie.valeur,
            equipment.equipementDureeVie.unite.text,
          ].join(' ')
        : null,
    excellencePoles: equipment.poleExcellences.map((pole) => pole.text),
    fundingProjects: equipment.projetFinancements.map(
      (project) => project.text,
    ),
    healthCanadaCompliance: equipment.estEssais
      ? "Cet équipement ne répond pas aux exigences de Santé Canada en terme d'essais cliniques"
      : null,
    holder: equipment.titulaire.text,
    installationDate: equipment.dateInstallation
      ? new Date(equipment.dateInstallation).toLocaleDateString('fr-CA')
      : null, // Format de date normal ou null
    inventoryNumber: equipment.numeroInventaire,
    jurisdiction: equipment.juridiction.text,
    location: equipment.emplacement?.text,
    mainEquipments: null,
    manufacturer: equipment.manufacturier?.text,
    manufacturingYear: equipment.anneeFabrication,
    measurementUncertainty: null,
    model: equipment.modele,
    parentInfrastructure: equipment.infrastructure.nom,
    purchaseDate: equipment.dateAchat
      ? new Date(equipment.dateAchat).toLocaleDateString('fr-CA')
      : null, // Format de date normal ou null
    relatedEquipments: equipment.equipementAutres.map(
      (eq) => eq.entiteLiee?.text,
    ),
    researchDomains: equipment.domaineRecherches.map((domain) => domain.text),
    safetyManagers: equipment.responsablesSST.map((manager) => manager.text),
    safetyRisks: equipment.risque,
    scientificPublications: null,
    socioEconomicObjectives: null,
    specifications: equipment.description,
    supplier: equipment.fournisseur?.text,
    technicalManagers: equipment.responsablesTechnique.map(
      (manager) => manager.text,
    ),
    techniques: equipment.techniques.map((technique) => technique.text),
    usageContexts: equipment.utilisation,
  };
};

export const mapEquipmentToMedia = (
  equipment: EquipmentFull,
): {
  documents: DocumentType[];
  photos: Pick<MediaType, 'href' | 'text' | 'thumbnail'>[];
  videos: Pick<MediaType, 'href' | 'text' | 'thumbnail'>[];
} => {
  return {
    documents: equipment.documents.map((document) => ({
      category: document.categorie.nom,
      contentType: document.typeContenu,
      createdAt: document.createdAt,
      description: document.description,
      filename: document.filename,
      href: document.href,
      id: document.id,
      isConfidential: document.estConfidentiel,
    })),
    photos: equipment.photos.map((photo) => ({
      href: photo.href,
      text: photo.text,
      thumbnail: photo.thumbnail,
    })),
    videos: equipment.videos.map((video) => ({
      href: video.href,
      text: video.text,
      thumbnail: video.thumbnail,
    })),
  };
};

export function mapEquipmentToDetails(equipment: EquipmentFull) {
  return {
    description: mapEquipmentToDescription(equipment),
    headerDescription: mapEquipmentToDescriptionDetails(equipment),
    maintenance: mapEquipmentToMaintenanceDetails(equipment),
    media: mapEquipmentToMedia(equipment),
    requirements: mapEquipmentToRequirementsDetails(equipment),
  };
}
