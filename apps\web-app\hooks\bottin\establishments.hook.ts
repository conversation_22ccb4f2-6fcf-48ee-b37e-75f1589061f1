import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import type { EstablishmentFormSchema } from '@/schemas/bottin/establishment-form-schema';
import {
  createGeneric,
  deleteGeneric,
  updateGeneric,
} from '@/services/bottin/generic-list.service';
import type { InstitutionEdit } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateEstablishment = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<InstitutionEdit, Error, EstablishmentFormSchema>({
    mutationFn: async (payload) =>
      (await createGeneric({
        controlledListKey: 'establishment',
        payload,
      })) as unknown as InstitutionEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'establishment', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Establishment created successfully',
        variant: 'success',
      });
      router.push('/bottin/etablissements');
    },
  });
};

export const useUpdateEstablishment = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<
    InstitutionEdit,
    Error,
    { id: string; payload: EstablishmentFormSchema }
  >({
    mutationFn: async ({ id, payload }) =>
      (await updateGeneric({
        controlledListKey: 'establishment',
        id,
        payload,
      })) as unknown as InstitutionEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'establishment', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Establishment updated successfully',
        variant: 'success',
      });
    },
  });
};

export const useDeleteEstablishment = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<void, Error, string>({
    mutationFn: async (id: string) => {
      await deleteGeneric({ controlledListKey: 'establishment', id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'establishment', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Establishment deleted successfully',
        variant: 'success',
      });
    },
  });
};
