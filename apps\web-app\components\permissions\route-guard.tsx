'use client';
import { useToast } from '@/components/hooks/use-toast';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useUserHasPermissionTo } from '@/hooks/useUserHasPermissionTo';
import { useSession } from '@/lib/better-auth';
import { redirect } from '@/lib/navigation';
import type { SupportedLocale } from '@/types/locale';
import type { PermissionAction, ResourceType } from '@rie/domain/types';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import { type PropsWithChildren, useEffect, useRef } from 'react';

type RouteGuardProps = {
  operation: PermissionAction;
  resource: ResourceType;
};

export const RouteGuard = ({
  children,
  operation,
  resource,
}: PropsWithChildren<RouteGuardProps>) => {
  const tCommon = useTranslations('common');
  const tPermissions = useTranslations('permission');
  const { data, isPending } = useSession();
  const userWarnedRef = useRef(false);
  const params = useParams<{ id: string; locale: SupportedLocale }>();
  const locale = useAvailableLocale();

  const { toast } = useToast();

  const description = tPermissions('routeDenied.message', {
    operation: tCommon(`operations.${operation}`),
    resource: tCommon(`resources.${resource}`),
  });

  const title = tPermissions('routeDenied.title');

  const { permissionStatus } = useUserHasPermissionTo({
    action: operation,
    resourceType: resource,
    resourceId: params.id,
  });

  if (isPending) {
    return <LoadingResource />;
  }

  useEffect(() => {
    if (permissionStatus === 'denied' && !userWarnedRef.current) {
      toast({
        description,
        duration: 5000,
        title,
        variant: 'destructive',
      });
      userWarnedRef.current = true;
    }
  }, [permissionStatus, toast, description, title]);

  redirect({ href: { pathname: '/' }, locale });

  return permissionStatus === 'granted' && data?.user ? children : null;
};
