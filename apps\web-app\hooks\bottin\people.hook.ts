import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import type { PersonFormSchema } from '@/schemas/bottin/person-form-schema';
import {
  createGeneric,
  deleteGeneric,
  updateGeneric,
} from '@/services/bottin/generic-list.service';
import type { PersonEdit } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreatePerson = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<PersonEdit, Error, PersonFormSchema>({
    mutationFn: async (payload) =>
      (await createGeneric({
        controlledListKey: 'person',
        payload,
      })) as unknown as PersonEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'person', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Person created successfully',
        variant: 'success',
      });
      router.push('/bottin/personnes');
    },
  });
};

export const useUpdatePerson = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<
    PersonEdit,
    Error,
    { id: string; payload: PersonFormSchema }
  >({
    mutationFn: async ({ id, payload }) =>
      (await updateGeneric({
        controlledListKey: 'person',
        id,
        payload,
      })) as unknown as PersonEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'person', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Person updated successfully',
        variant: 'success',
      });
    },
  });
};

export const useDeletePerson = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<void, Error, string>({
    mutationFn: async (id: string) => {
      await deleteGeneric({ controlledListKey: 'person', id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'person', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Person deleted successfully',
        variant: 'success',
      });
    },
  });
};
