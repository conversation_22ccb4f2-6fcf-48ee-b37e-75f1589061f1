import Details from '@/app/[locale]/contrats-service/[id]/details';
import { RouteGuard } from '@/components/permissions/route-guard';
import { getQueryClientOptions } from '@/constants/query-client';
import { serviceContractByIdOptions } from '@/hooks/contract-service/useGetContractServiceById';
import type { PageDetailsParams } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

export default async function ContratServiceDescription(
  props: PageDetailsParams,
) {
  const params = await props.params;

  const { id, locale } = params;

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  await queryClient.prefetchQuery(serviceContractByIdOptions(id, locale));
  return (
    <RouteGuard operation="read" resource="serviceContract">
      <HydrationBoundary state={dehydrate(queryClient)}>
        <Details />
      </HydrationBoundary>
    </RouteGuard>
  );
}
