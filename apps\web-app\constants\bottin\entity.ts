export const initialColumnVisibility = {
  // TODO: Ajouter les autres types d'entités
  building: {},
  campus: {},
  institution: {
    acronym: true,
    lastUpdatedAt: true,
    name: true,
    type: true,
    // nameEnglish: true,
  },
  fundingProject: {},
  vendor: {
    dateEnd: true,
    lastUpdatedAt: true,
    name: true,
    parent: true,
  },
  people: {
    emails: true,
    family_name: true,
    given_name: true,
    last_updated_at: true,
  },
  room: {},
  unit: {
    lastUpdatedAt: true,
    name: true,
    // nameEnglish: true,
    parentUnit: true,
  },
} as const;

export const entityFormSections = {
  description: {
    key: 'description',
    order: 1,
  },
} as const;
