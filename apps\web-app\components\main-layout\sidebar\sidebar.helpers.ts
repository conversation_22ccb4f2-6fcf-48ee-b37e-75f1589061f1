import {
  getUserPermissionTo,
  userIsAdmin,
} from '@/helpers/permissions.helpers';
import type { PermissionMap } from '@/types/permission.type';
import type { User } from '@/types/user';

import type { MenuItem } from './sidebar.types';

type GetMenuItemsForUserArgs = {
  menuItems: MenuItem[];
  permissionsMap?: PermissionMap;
  user: null | User;
};

export const getMenuItemsForUser = ({
  menuItems,
  permissionsMap,
  user,
}: GetMenuItemsForUserArgs) => {
  return !user || !permissionsMap
    ? menuItems.filter((item) => item.public)
    : menuItems.filter((item) => {
        const hasReadPermission =
          getUserPermissionTo({
            operation: 'read',
            permissionsMap,
            resource: item.key,
            user,
          }) === 'granted';

        const isAdmin = userIsAdmin(user);

        // TODO: once we are ready to see these items in production we should remove the condition
        // Only include directory/serviceContract if admin OR (has permission AND not production)
        const includeSpecialItems =
          isAdmin ||
          (hasReadPermission && process.env.NODE_ENV !== 'production');

        return (
          item.public ||
          item.key === 'profile' ||
          (hasReadPermission &&
            !(
              ['directory', 'serviceContract'].includes(item.key) && !isAdmin
            )) || // Has permission and is not a special item for non-admins
          (includeSpecialItems &&
            ['directory', 'serviceContract'].includes(item.key)) // Include special items if conditions are met
        );
      });
};
