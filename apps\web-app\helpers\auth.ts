import { REFRESH_TOKEN_EXPIRATION_THRESHOLD } from '@/constants/common';
import {
  EQUIPMENT_INFRASTRUCTURE_ROUTES,
  PROTECTED_ROUTE_PATTERNS,
} from '@/constants/routes';
import dayjs from '@/lib/dayjs';
import type { Dayjs } from 'dayjs';

export const isTokenAboutToExpire = (date: Date | Dayjs | number | string) => {
  return (
    dayjs.duration(dayjs(date).diff(dayjs())).minutes() <
    REFRESH_TOKEN_EXPIRATION_THRESHOLD
  );
};

export const isValidToken = ({
  accessTokenValue,
  expiresInValue,
}: {
  accessTokenValue?: string;
  expiresInValue?: string;
}) =>
  accessTokenValue && expiresInValue && !isTokenAboutToExpire(expiresInValue);

const matchesPattern = (path: string, pattern: string): boolean => {
  const regexPattern = pattern
    .replace(/:path\*/g, '___PATH_WILDCARD___') // Temporary placeholder for :path*
    .replace(/:id\*/g, '[^/]+') // :id* matches any segment except /
    .replace(/\*/g, '[^/]+') // * matches any segment except /
    .replace(/___PATH_WILDCARD___/g, '.*') // :path* matches any path including multiple segments
    .replace(/\//g, '\\/'); // Escape forward slashes

  return new RegExp(`^${regexPattern}$`).test(path);
};

export const isProtectedRoute = (path: string): boolean => {
  // Extract just the pathname portion, removing query parameters and hash fragments
  let pathname = path.split('?')[0].split('#')[0];

  // Remove trailing slash for consistency (except for root path)
  if (pathname.length > 1 && pathname.endsWith('/')) {
    pathname = pathname.slice(0, -1);
  }

  return PROTECTED_ROUTE_PATTERNS.some((pattern) =>
    matchesPattern(pathname.toLowerCase(), pattern.toLowerCase()),
  );
};

export const isInfrastructureOrEquipmentRoute = (path: string): boolean => {
  return EQUIPMENT_INFRASTRUCTURE_ROUTES.some((pattern) => pattern.test(path));
};
