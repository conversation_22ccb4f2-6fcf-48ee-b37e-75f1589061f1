import { NewInfrastructureForm } from '@/app/[locale]/infrastructures/ajouter/new-infrastructure-form';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { RouteGuard } from '@/components/permissions/route-guard';
import { infrastructureFormSections } from '@/constants/infrastructures';
import { getQueryClientOptions } from '@/constants/query-client';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { BasePageParams, ControlledListKey } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { setRequestLocale } from 'next-intl/server';

export default async function NewInfrastructurePage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  setRequestLocale(locale);

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const controlledLists: ControlledListKey[] = [
    'person',
    'organisation',
    'unit',
    'fundingProjects',
    'local',
    'visibility',
    'infrastructureType',
    'infrastructureStatus',
    'innovationLab',
  ];

  await Promise.all(
    controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  );

  const formSections = await getFormSections({
    resourceName: 'infrastructures',
    sections: infrastructureFormSections,
  });

  return (
    <RouteGuard operation="create" resource="infrastructure">
      <HydrationBoundary state={dehydrate(queryClient)}>
        <NewInfrastructureForm formSections={formSections} locale={locale} />
      </HydrationBoundary>
    </RouteGuard>
  );
}
