import type {
  CollectionBuildingResultType,
  ResourceBuildingResultType,
} from '@/services/buildings/buildings.service';
import {
  getAllBuildings,
  getBuildingById,
} from '@/services/buildings/buildings.service';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { queryOptions } from '@tanstack/react-query';

export const getAllBuildingsOptions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) => {
  return queryOptions<CollectionBuildingResultType<View>>({
    queryFn: () => getAllBuildings({ view }),
    queryKey: ['buildings', { view }],
  });
};

interface GetBuildingByIdParams {
  id: string;
  view: ResourceViewType;
}

export const getBuildingByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: GetBuildingByIdParams) => {
  return queryOptions<ResourceBuildingResultType<View>>({
    queryFn: () => getBuildingById({ id, view }),
    queryKey: ['buildings', id, view],
    enabled: !!id,
  });
};
