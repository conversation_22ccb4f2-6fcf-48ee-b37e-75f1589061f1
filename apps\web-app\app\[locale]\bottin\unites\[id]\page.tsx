import type { BasePageParams } from '@/types/common';
import { setRequestLocale } from 'next-intl/server';

type UnitsPageParams = {
  params: {
    id: string;
  };
} & BasePageParams;

export default async function UnitsPage(props: UnitsPageParams) {
  const params = await props.params;

  const { locale } = params;

  setRequestLocale(locale);

  return (
    <>
      <div>HELLO FROM ID</div>
    </>
  );
}
