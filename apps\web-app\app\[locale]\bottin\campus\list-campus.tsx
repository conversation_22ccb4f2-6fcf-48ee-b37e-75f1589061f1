'use client';

import { campusColumns } from '@/app/[locale]/bottin/campus/columns';
import { GenericList } from '@/components/bottin-table/generic-list';
import { initialColumnVisibility } from '@/constants/bottin/campus';
import type { SupportedLocale } from '@/types/locale';
import type { CampusList as CampusListType } from '@rie/domain/types';

type CampusListProps = {
  locale: SupportedLocale;
};

export const CampusList = ({ locale }: CampusListProps) => {
  return (
    <GenericList<'campus', 'list', CampusListType, Record<string, unknown>>
      controlledListKey="campus"
      view="list"
      locale={locale}
      columns={campusColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="campus"
    />
  );
};
