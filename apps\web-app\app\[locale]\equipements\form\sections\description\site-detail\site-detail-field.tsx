import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { useGetSiteCategories } from '@/app/[locale]/equipements/form/sections/description/site-detail/hooks/useGetSiteCategories';
import { FieldInfo } from '@/components/FieldInfo';
import { ComboboxField } from '@/components/form-fields/combobox-field';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Input } from '@/ui/input';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

type SiteDetailProps = {
  index: number;
};

export const SiteDetailField = ({ index }: SiteDetailProps) => {
  const tEquipments = useTranslations(
    'equipments.form.sections.description.siteDetail',
  );
  const tCommon = useTranslations('common');
  const { control, formState } = useFormContext<EquipmentFormSchema>();

  const descriptiveFields = useTranslatedField(
    control,
    `siteDetails.${index}.descriptive`,
  );
  const descriptiveError = getFieldErrorMessage(
    formState.errors,
    `siteDetails.${index}.descriptive`,
  );
  const descriptiveErrorMessage = descriptiveError
    ? tEquipments(descriptiveError)
    : undefined;

  //TODO: use an infinite list or change the combobox
  const siteCategories = useGetSiteCategories();

  return (
    <>
      <FieldWithTranslations
        control={control}
        errorMessage={descriptiveErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName={`siteDetails.${index}.descriptive`}
        fields={descriptiveFields.fields}
        label={(locale) =>
          tEquipments('fields.descriptive.labelWithLocale', {
            locale: tCommon(locale),
          })
        }
        onAddTranslation={descriptiveFields.handleAddTranslation}
        onRemoveTranslation={descriptiveFields.handleRemoveTranslation}
      />
      <FormField
        control={control}
        name={`siteDetails.${index}.url`}
        render={({ field }) => (
          <FormItem>
            <LabelTooltip label={tEquipments('fields.url.label')} />
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
            </FieldInfo>
          </FormItem>
        )}
      />
      <ComboboxField
        clearErrorsOnChange={false}
        fieldLabel={tEquipments('fields.siteCategory.label')}
        fieldName={`siteDetail.${index}.siteCategory`}
        options={siteCategories.map((category) => ({
          label: category.label,
          value: category.id,
        }))}
        placeholder={tCommon('select')}
      />
    </>
  );
};
