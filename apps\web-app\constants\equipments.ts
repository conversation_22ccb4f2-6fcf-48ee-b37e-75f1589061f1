import {
  CAMPUS_ADDRESS_DEFAULT_VALUE,
  NULLABLE_SELECT_OPTION,
} from '@/constants/common';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import type { SupportedLocale } from '@/types/locale';

export const equipmentStatus = {
  donne: 'removed',
  egare: 'destructive',
  'en-acquisition': 'warning',
  'en-deplacement': 'warning',
  'en-developpement': 'warning',
  'en-reparation': 'warning',
  fonctionnel: 'success',
  'hors-service': 'destructive',
  jete: 'destructive',
  'partiellement-fonctionnel': 'notice',
  vendu: 'removed',
  vole: 'destructive',
} as const;

export const equipmentFormSections = {
  description: {
    key: 'description',
    order: 1,
  },
  exigenceOfService: {
    key: 'exigenceOfService',
    order: 3,
  },
  maintenance: {
    key: 'affiliations',
    order: 2,
  },
  permissions: {
    key: 'permissions',
    order: 4,
  },
} as const;

export const equipmentFormDefaultValues = (
  locale: SupportedLocale,
): EquipmentFormSchema => ({
  accessories: [],
  acquisitionCostInCash: '0',
  acquisitionCostInNature: '0',
  associatedFinancingProjects: [],
  address: CAMPUS_ADDRESS_DEFAULT_VALUE,
  categories: [],
  comments: [{ locale, value: '' }],
  components: [],
  dateOfDecommission: undefined,
  dateOfInstallation: undefined,
  dateOfPurchase: undefined,
  depth: '',
  description: [{ locale, value: '' }],
  documents: [],
  DOIOfArticles: '',
  equipmentHolder: NULLABLE_SELECT_OPTION,
  equipmentUnit: NULLABLE_SELECT_OPTION,
  estimatedLifespan: '',
  excellencePole: [],
  height: '',
  hideEquipment: false,
  highlight: false,
  images: [],
  immaterialEquipment: [],
  infrastructure: NULLABLE_SELECT_OPTION,
  inventoryNumber: '',
  isSafe: false,
  jurisdiction: NULLABLE_SELECT_OPTION,
  manufacturerProvider: {
    manufacturer: NULLABLE_SELECT_OPTION,
    supplier: NULLABLE_SELECT_OPTION,
  },
  maximalMaintenanceLength: '',
  measurements: [],
  minimalMaintenanceRequired: [{ locale, value: '' }],
  model: '',
  name: [{ locale, value: '' }],
  otherEquipments: [],
  percentageFunctionality: 100,
  registrationNumber: '',
  repairers: [],
  researchFields: [],
  serialNumber: '',
  servicesContract: [],
  siteDetails: [],
  socioEconomicObjectives: [],
  specifications: [{ locale, value: '' }],
  sstManager: [],
  sstRisks: [{ locale, value: '' }],
  status: NULLABLE_SELECT_OPTION,
  technicalManager: [],
  technics: [],
  termsOfDisposal: [{ locale, value: '' }],
  type: NULLABLE_SELECT_OPTION,
  unit: NULLABLE_SELECT_OPTION,
  usageContexts: [{ locale, value: '' }],
  videos: [],
  weight: '',
  width: '',
  yearOfManufacture: 0,
});

export const IMAGE_MAX_UPLOAD_SIZE_MB = 5;
export const VIDEO_MAX_UPLOAD_SIZE_MB = 100;
export const FILE_MAX_UPLOAD_SIZE_MB = 100;
const SIZE_MB = 1024 * 1024;
export const IMAGE_MAX_UPLOAD_SIZE = SIZE_MB * IMAGE_MAX_UPLOAD_SIZE_MB; // 5MB
export const VIDEO_MAX_UPLOAD_SIZE = SIZE_MB * VIDEO_MAX_UPLOAD_SIZE_MB; // 5MB
export const FILE_MAX_UPLOAD_SIZE = SIZE_MB * FILE_MAX_UPLOAD_SIZE_MB; // 100MB
