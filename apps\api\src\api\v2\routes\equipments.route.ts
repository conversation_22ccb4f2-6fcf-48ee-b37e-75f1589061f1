import { handleEffectError } from '@/api/v2/utils/error-handler';
import { EquipmentsRuntime } from '@/infrastructure/runtimes/equipments.runtime';
import { effectValidator } from '@hono/effect-validator';
import {
  CollectionViewParamSchema,
  EquipmentInputSchema,
  EquipmentListSchema,
  EquipmentSchema,
  LimitParamSchema,
  ResourceIdSchema,
} from '@rie/domain/schemas';
import {
  dbEquipmentToResource,
  dbEquipmentsToCollection,
} from '@rie/domain/serializers';
import type { HonoVariables } from '@rie/domain/types';
import { EquipmentsServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllEquipmentsRoute = describeRoute({
  description: 'Lister tous les équipements',
  operationId: 'getAllEquipments',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue (list, grid, select)',
    },
    {
      name: 'limit',
      in: 'query',
      required: false,
      schema: resolver(LimitParamSchema),
      description: 'Limiter le nombre retourné (0 pour seulement le count)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(EquipmentListSchema)),
        },
      },
      description: 'Équipements retournés',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

export const getEquipmentByIdRoute = describeRoute({
  description: 'Obtenir un équipement par ID',
  operationId: 'getEquipmentById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'équipement (format CUID)",
      example: 'equip123abc456def789ghi',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(EquipmentSchema),
        },
      },
      description: 'Équipement trouvé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Équipement non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

export const createEquipmentRoute = describeRoute({
  description: 'Créer un équipement',
  operationId: 'createEquipment',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(EquipmentInputSchema),
        example: {
          model: 'Advanced Microscope X1000',
          serialNumber: 'AM-X1000-2024-001',
          typeId: 'equipment_type_001',
          useInClinicalTrial: false,
          isHidden: false,
          workingPercentage: 95.5,
          monetaryCost: 150000.0,
          manufactureYear: 2024,
          acquisitionDate: '2024-01-15',
          installationDate: '2024-02-01',
          scientificManagerId: 'w4ku5n46mdvkv5bjuuml2j3w',
          manufacturerId: 'wjbwu31n6ifz8ijm7g7v2b2m',
          institutionId: 'sydsapwdh5p2m9z9tvnde2dp',
          isFeatured: false,
          translations: [
            {
              locale: 'en',
              name: 'Advanced Research Microscope',
              description:
                'High-resolution microscope for advanced research applications',
              specification: 'Resolution: 0.1nm, Magnification: 1000x-100000x',
              usageContext: 'Research laboratories, material science',
              risk: 'Low risk when operated by trained personnel',
              comment: 'Requires specialized training for operation',
            },
            {
              locale: 'fr',
              name: 'Microscope de Recherche Avancé',
              description:
                'Microscope haute résolution pour applications de recherche avancées',
              specification: 'Résolution: 0.1nm, Grossissement: 1000x-100000x',
              usageContext: 'Laboratoires de recherche, science des matériaux',
              risk: "Risque faible lorsqu'utilisé par du personnel formé",
              comment: "Nécessite une formation spécialisée pour l'utilisation",
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(EquipmentSchema),
        },
      },
      description: 'Équipement créé',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Clé étrangère non trouvée - Le type, gestionnaire, manufacturier ou institution n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

export const updateEquipmentRoute = describeRoute({
  description: 'Mettre à jour un équipement',
  operationId: 'updateEquipment',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'équipement (format CUID)",
      example: 'equip123abc456def789ghi',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(EquipmentInputSchema),
        example: {
          model: 'Updated Advanced Microscope X2000',
          serialNumber: 'AM-X2000-2024-001',
          typeId: 'equipment_type_002',
          workingPercentage: 98.0,
          monetaryCost: 175000.0,
          manufactureYear: 2024,
          acquisitionDate: '2024-03-15',
          installationDate: '2024-04-01',
          scientificManagerId: 'w4ku5n46mdvkv5bjuuml2j3w',
          manufacturerId: 'wjbwu31n6ifz8ijm7g7v2b2m',
          institutionId: 'sydsapwdh5p2m9z9tvnde2dp',
          isFeatured: true,
          translations: [
            {
              locale: 'en',
              name: 'Updated Advanced Research Microscope',
              description:
                'Updated high-resolution microscope for advanced research',
              specification: 'Resolution: 0.05nm, Magnification: 2000x-200000x',
              usageContext: 'Advanced research laboratories, nanotechnology',
              risk: 'Low risk with enhanced safety features',
              comment: 'Updated model with improved capabilities',
            },
            {
              locale: 'fr',
              name: 'Microscope de Recherche Avancé Mis à Jour',
              description:
                'Microscope haute résolution mis à jour pour la recherche avancée',
              specification: 'Résolution: 0.05nm, Grossissement: 2000x-200000x',
              usageContext:
                'Laboratoires de recherche avancés, nanotechnologie',
              risk: 'Risque faible avec fonctionnalités de sécurité améliorées',
              comment: 'Modèle mis à jour avec capacités améliorées',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(EquipmentSchema),
        },
      },
      description: 'Équipement mis à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Équipement non trouvé ou clé étrangère non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

export const deleteEquipmentRoute = describeRoute({
  description: 'Supprimer un équipement',
  operationId: 'deleteEquipment',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'équipement (format CUID)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Équipement supprimé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Équipement non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Equipments'],
});

const equipmentsRoute = new Hono<{
  Variables: HonoVariables;
}>();

equipmentsRoute.get(
  '/',
  getAllEquipmentsRoute,
  effectValidator(
    'query',
    Schema.Struct({
      view: Schema.optional(CollectionViewParamSchema.fields.view),
      limit: Schema.optional(LimitParamSchema),
      relations: Schema.optional(
        Schema.String.pipe(Schema.pattern(/^[a-z,]*$/)),
      ),
    }),
  ),
  async (ctx) => {
    const { limit, view = 'list', relations } = ctx.req.valid('query');
    if (limit === 0) {
      const program = Effect.gen(function* () {
        const equipmentService = yield* EquipmentsServiceLive;
        return yield* equipmentService.countEquipments();
      });
      const result = await EquipmentsRuntime.runPromiseExit(program);
      const errorResponse = handleEffectError(ctx, result);
      if (errorResponse) {
        return errorResponse;
      }

      if (Exit.isSuccess(result)) {
        return ctx.json({ count: result.value });
      }

      return ctx.json({ error: 'An error occurred' }, 500);
    }

    const program = Effect.gen(function* () {
      const equipmentService = yield* EquipmentsServiceLive;
      const includes = (() => {
        const flags = new Set((relations ?? '').split(',').filter(Boolean));
        return {
          type: flags.has('type'),
          status: flags.has('status'),
          unit: flags.has('unit'),
          address: flags.has('address'),
        } as const;
      })();
      const rows = yield* equipmentService.getAllEquipments(includes);
      return dbEquipmentsToCollection(rows, view);
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

equipmentsRoute.get(
  '/:id',
  getEquipmentByIdRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator(
    'query',
    Schema.Union(
      Schema.Struct({
        view: Schema.Literal('detail'),
        relations: Schema.optional(Schema.String),
      }),
      Schema.Struct({
        view: Schema.Literal('edit'),
        relations: Schema.optional(Schema.String),
      }),
    ),
  ),
  async (ctx) => {
    const id = ctx.req.param('id');
    const { view } = ctx.req.valid('query') as {
      view: 'detail' | 'edit';
      relations?: string;
    };
    const program = Effect.gen(function* () {
      const equipmentService = yield* EquipmentsServiceLive;
      const row = yield* equipmentService.getEquipmentById(id);
      return dbEquipmentToResource(row, view);
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

equipmentsRoute.post(
  '/',
  createEquipmentRoute,
  effectValidator('json', EquipmentInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const equipmentService = yield* EquipmentsServiceLive;
      return yield* equipmentService.createEquipment({
        ...body,
        modifiedBy: user?.id,
      });
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

equipmentsRoute.put(
  '/:id',
  updateEquipmentRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('json', EquipmentInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const equipmentService = yield* EquipmentsServiceLive;
      return yield* equipmentService.updateEquipment({
        id,
        equipment: {
          ...body,
          modifiedBy: user?.id,
        },
      });
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

equipmentsRoute.delete(
  '/:id',
  deleteEquipmentRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const equipmentService = yield* EquipmentsServiceLive;
      return yield* equipmentService.deleteEquipment(id);
    });
    const result = await EquipmentsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Equipment deleted' });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { equipmentsRoute };
