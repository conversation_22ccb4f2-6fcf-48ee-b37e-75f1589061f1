import { PgDatabaseLayer } from '@rie/postgres-db';
import { VendorsRepositoryLive } from '@rie/repositories';
import { VendorsServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const VendorServicesLayer = Layer.mergeAll(
  VendorsRepositoryLive.Default,
  VendorsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const VendorsRuntime = ManagedRuntime.make(VendorServicesLayer);
