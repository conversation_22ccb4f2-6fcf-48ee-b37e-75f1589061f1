import { mapEquipmentFullToFormSchema } from '@/app/[locale]/equipements/helpers/map-equipment-to-form';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { RouteGuard } from '@/components/permissions/route-guard';
import { equipmentFormSections } from '@/constants/equipments';
import { getQueryClientOptions } from '@/constants/query-client';
import { defaultRieServiceParams } from '@/constants/rie-client';
import { allServiceContractOptions } from '@/hooks/contract-service/useGetAllServiceContracts';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import { allEquipmentsOptions } from '@/hooks/equipment/useGetAllEquipments';
import { equipmentByIdOptions } from '@/hooks/equipment/useGetEquipmentById';
import { allInfrastructuresOptions } from '@/hooks/infrastructure/useGetAllInfrastructures';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';

import { EditEquipmentForm } from './edit-equipment-form';

export default async function EditEquipmentPage(props: PageDetailsParams) {
  const params = await props.params;

  const { id, locale } = params;

  const queryClient = new QueryClient(getQueryClientOptions(locale));
  const controlledLists: ControlledListKey[] = [
    'supplier',
    'equipmentType',
    'organisation',
    'person',
    'building',
    'local',
    'equipmentStatus',
    'equipmentCategory',
    'applicationSector',
    'researchDomain',
    'excellenceCenter',
    'technic',
    'fundingProjects',
  ];
  await Promise.all([
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
    await queryClient.prefetchQuery(
      equipmentByIdOptions(id, locale, {
        select: (data) => mapEquipmentFullToFormSchema(data.equipment, locale),
      }),
    ),
    queryClient.prefetchQuery(
      allInfrastructuresOptions({
        params: defaultRieServiceParams(locale),
        queryParams: '',
      }),
    ),
    queryClient.prefetchQuery(
      allEquipmentsOptions({
        params: defaultRieServiceParams(locale),
        queryParams: '',
      }),
    ),
    queryClient.prefetchQuery(allServiceContractOptions(locale)),
  ]);

  const formSections = await getFormSections({
    resourceName: 'equipments',
    sections: equipmentFormSections,
  });

  return (
    <RouteGuard operation="update" resource="equipment">
      <HydrationBoundary state={dehydrate(queryClient)}>
        <EditEquipmentForm
          formSections={formSections}
          id={id}
          locale={locale}
        />
      </HydrationBoundary>
    </RouteGuard>
  );
}
