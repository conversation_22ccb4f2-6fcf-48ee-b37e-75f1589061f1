import type { RieServiceParams } from '@/constants/rie-client';
import { infiniteInfrastructuresOptions } from '@/hooks/infrastructure/useGetInfiniteInfrastructuresOptions';
import type { ApiInfiniteReturnType } from '@/types/common';
import type { InfrastructureFull } from '@/types/infrastructure';
import {
  useInfiniteQuery,
  type UseInfiniteQueryOptions,
} from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import { useSearchParams } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useMemo } from 'react';

type UseGetInfiniteInfrastructuresArgs<TData> = {
  params: RieServiceParams;
  queryParams?: string;
} & Pick<
  UseInfiniteQueryOptions<
    ApiInfiniteReturnType<InfrastructureFull[]>,
    AxiosError,
    TData
  >,
  'select'
>;

export const useGetInfiniteInfrastructures = <TData>({
  params,
  queryParams,
  select,
}: UseGetInfiniteInfrastructuresArgs<TData>) => {
  // Using both nuqs for simple params and searchParams for the full URL
  const [searchQuery] = useQueryState('q');
  const [sortValue] = useQueryState('sort');
  const [viewOwn] = useQueryState('userdata', { defaultValue: '0' });
  const searchParams = useSearchParams();

  // Create the combined query parameters string
  const combinedQueryParams = useMemo(() => {
    const allParams = new URLSearchParams();

    // Add search query if it exists
    if (searchQuery) {
      allParams.append('q', searchQuery);
    }

    // Add sort value if it exists
    if (sortValue) {
      allParams.append('sort', sortValue);
    }

    // Add any explicit queryParams passed to the hook
    if (queryParams) {
      const customParams = new URLSearchParams(queryParams);
      for (const [key, value] of Array.from(customParams.entries())) {
        allParams.append(key, value);
      }
    }

    // Add all filter parameters from the URL
    for (const [key, value] of Array.from(searchParams.entries())) {
      if (key.startsWith('ff[')) {
        allParams.append(key, value);
      }
    }

    return allParams.toString();
  }, [searchQuery, sortValue, queryParams, searchParams]);

  // Check if the user wants to see only their own resources
  const viewOwnInfrastructures = viewOwn === '1';

  return useInfiniteQuery(
    infiniteInfrastructuresOptions({
      params,
      queryParams: combinedQueryParams,
      select,
      viewOwnInfrastructures,
    }),
  );
};
