import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import type { FinancingProjectFormSchema } from '@/schemas/bottin/financing-project-form-schema';
import {
  createGeneric,
  deleteGeneric,
  updateGeneric,
} from '@/services/bottin/generic-list.service';
import type { FundingProjectEdit } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateFundingProject = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<FundingProjectEdit, Error, FinancingProjectFormSchema>({
    mutationFn: async (payload) =>
      (await createGeneric({
        controlledListKey: 'fundingProjects',
        payload,
      })) as unknown as FundingProjectEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'fundingProjects', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Funding project created successfully',
        variant: 'success',
      });
      router.push('/bottin/projets-financement');
    },
  });
};

export const useUpdateFundingProject = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<
    FundingProjectEdit,
    Error,
    { id: string; payload: FinancingProjectFormSchema }
  >({
    mutationFn: async ({ id, payload }) =>
      (await updateGeneric({
        controlledListKey: 'fundingProjects',
        id,
        payload,
      })) as unknown as FundingProjectEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'fundingProjects', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Funding project updated successfully',
        variant: 'success',
      });
    },
  });
};

export const useDeleteFundingProject = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<void, Error, string>({
    mutationFn: async (id: string) => {
      await deleteGeneric({ controlledListKey: 'fundingProjects', id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'fundingProjects', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Funding project deleted successfully',
        variant: 'success',
      });
    },
  });
};
