{"table": {"columns": {"domain": "Domaine", "action": "Action", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "actions": "Actions"}}, "domains": {"address": "<PERSON><PERSON><PERSON>", "applicationSector": "Secteur d'application", "building": "Bâtiment", "campus": "Campus", "documentationCategory": "Catégorie de documentation", "equipment": "Équipement", "equipmentCategory": "Catégorie d'équipement", "equipmentStatus": "État d'équipement", "equipmentType": "Type d'équipement", "excellenceHub": "Pôle d'excellence", "fundingProject": "Projet de financement", "fundingProjectType": "Type de projet de financement", "fundingProjectIdentifierType": "Type d'identifiant de projet de financement", "infrastructure": "Infrastructure", "infrastructureStatus": "Statut d'infrastructure", "infrastructureType": "Type d'infrastructure", "innovationLab": "Laboratoire d'innovation", "institution": "Établissement", "institutionType": "Type d'établissement", "media": "Média", "mediaType": "Type de média", "people": "<PERSON><PERSON>", "peopleRoleType": "Type de rôle de personne", "researchField": "Domaine de recherche", "room": "Local", "roomCategory": "Catégorie de local", "serviceContract": "Contrat de service", "serviceOffer": "Offre de service", "technique": "Technique", "unit": "Unité", "unitType": "Type d'unité", "user": "Utilisa<PERSON>ur", "vendor": "Fournisseur", "visibility": "Visibilité"}, "actions": {"create": "<PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON>", "update": "Mettre à jour", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "routeDenied": {"title": "Permission refusée", "message": "Vous n'avez pas la permission d'effectuer l'opération {operation} sur la ressource {resource}."}}