{"form": {"sections": {"description": {"title": "Description", "generalInfo": {"title": "Informations générales", "fields": {"buildingName": {"label": "Nom du bâtiment en {locale}", "error": {"required": "Nom du bâtiment est requis", "max": "Le nom du bâtiment ne peut pas contenir plus de {max} caractères"}}, "alias": {"label": "<PERSON>as en {locale}", "tooltip": "Autres ou anciens noms ou adresses civiques utilisés pour référer au même bâtiment", "error": {"max": "Alias ne peut pas contenir plus de {max} caractères"}}, "campus": {"label": "Campus", "error": {"required": "Campus is required"}}, "jurisdiction": {"label": "Juridiction", "error": {"required": "Juridiction est requis", "max": "La juridiction peut pas dépasser {max} caractères"}}, "address": {"label": "Adresse civique"}}, "addAddress": {"label": "Ajouter une adresse"}}}}}}