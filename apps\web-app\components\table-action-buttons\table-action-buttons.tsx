'use client';

import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { Button } from '@/components/ui/button';
import { useDeleteRole } from '@/hooks/admin/permissions/roles.hook';
import type { AppEditPathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import { useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

interface RoleActionsProps {
  id: string;
  name: string;
  pathname: AppEditPathnames;
}

export const TableActionButtons = ({
  id,
  name,
  pathname,
}: RoleActionsProps) => {
  const tCommon = useTranslations('common');
  const { mutate: deleteRole } = useDeleteRole();

  const handleDelete = () => {
    deleteRole(id);
  };

  return (
    <div className="flex justify-around items-center gap-x-2">
      <Button
        variant="ghost"
        asChild
        size="icon"
        className="hover:bg-white hover:shadow-lg hover:shadow-blue-500/25 transition-shadow duration-200"
      >
        <Link
          href={{
            pathname,
            params: { id },
          }}
        >
          <FaEdit className="h-4 w-4 text-blue-700" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={handleDelete}
        title={tCommon('deleteItemQuestion', { item: name })}
        trigger={
          <Button
            variant="ghost"
            size="icon"
            className="hover:bg-white hover:shadow-lg hover:shadow-blue-500/25 transition-shadow duration-200"
          >
            <MdDeleteForever className="h-4 w-4 text-destructive" />
          </Button>
        }
      />
    </div>
  );
};
