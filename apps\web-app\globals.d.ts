import '@tanstack/react-query';
// import type { locales } from '@/i18n/settings';
// import type * as messages from '@/locales/en';
import type { HTTPError } from 'ky';

declare module '@tanstack/react-query' {
  interface Register {
    defaultError: HTTPError;
  }
}

// TODO: Uncomment to add types to next-intl
// declare module 'next-intl' {
//   interface AppConfig {
//     Locales: (typeof locales)[number];
//     Messages: typeof messages;
//   }
// }
