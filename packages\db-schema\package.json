{"name": "@rie/db-schema", "version": "1.0.0", "type": "module", "description": "Database schema package", "main": "./build/index.js", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "exports": {".": {"types": "./build/dts/index.d.ts", "import": "./build/index.js", "require": "./build/index.js"}, "./entity-schemas": {"types": "./build/dts/entity-schemas/index.d.ts", "import": "./build/entity-schemas/index.js", "require": "./build/entity-schemas/index.js"}, "./entity-types": {"types": "./build/dts/entity-types/index.d.ts", "import": "./build/entity-types/index.js", "require": "./build/entity-types/index.js"}, "./schemas": {"types": "./build/dts/schemas/index.d.ts", "import": "./build/schemas/index.js", "require": "./build/schemas/index.js"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json && tsc-alias -p tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w && tsc-alias -p tsconfig.build.json", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write", "db:generate": "drizzle-kit generate --config drizzle.config.ts", "db:migrate": "drizzle-kit migrate --config drizzle.config.ts", "db:push": "drizzle-kit push --config drizzle.config.ts", "db:studio": "drizzle-kit studio --config drizzle.config.ts", "db:start": "./start-db.sh"}, "dependencies": {"@handfish/drizzle-effect": "^0.0.17", "@rie/biome-config": "workspace:*", "@rie/utils": "workspace:*", "@t3-oss/env-core": "^0.13.8", "pg": "^8.16.2"}, "peerDependencies": {"drizzle-orm": "^0.44.2", "effect": "^3.16.9"}, "devDependencies": {"@types/node": "^24.0.3", "@types/pg": "^8.15.4", "drizzle-kit": "^0.31.2", "tsc-alias": "^1.8.16", "typescript": "^5.8.3"}}