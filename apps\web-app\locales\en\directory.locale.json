{"title": {"manufacturiers": "Manufacturers", "etablissements": "Establishments", "unites": "Units", "personnes": "Persons", "locaux": "Locals", "batiments": "Buildings", "campus": "Campus", "projetsFinancement": "Funding Projects"}, "supplier": {"table": {"columns": {"name": "Name", "dateEnd": "Closing Date", "lastUpdatedAt": "Last Updated", "actions": "Actions"}}}, "institution": {"table": {"columns": {"name": "Name", "dateEnd": "Closing Date", "lastUpdatedAt": "Last Updated", "acronym": "Acronym", "establishmentType": "Type", "actions": "Actions"}}}, "unit": {"table": {"columns": {"name": "Name", "dateEnd": "Closing Date", "lastUpdatedAt": "Last Updated", "actions": "Actions", "parentName": "Parent", "acronym": "Acronym"}}}, "people": {"table": {"columns": {"name": "Name", "dateEnd": "Closing Date", "lastUpdatedAt": "Last Updated", "actions": "Actions", "email": "Email", "lastName": "Family Name", "firstName": "Given Name"}}}, "room": {"table": {"columns": {"name": "Room Number", "numero": "Number", "area": "Area", "dateEnd": "Closing Date", "lastUpdatedAt": "Last Updated", "actions": "Actions", "jurisdiction": "Juris<PERSON>", "building": "Building"}}}, "building": {"table": {"columns": {"name": "Building name", "dateEnd": "Closing Date", "lastUpdatedAt": "Last Updated", "actions": "Actions", "jurisdiction": "Juris<PERSON>", "campus": "Campus", "otherNames": "Other Names"}}}, "campus": {"description": {"title": "Description"}, "table": {"columns": {"name": "Campus name", "dateEnd": "Closing Date", "lastUpdatedAt": "Last Updated", "actions": "Actions", "jurisdiction": "Juris<PERSON>"}}}, "fundingProject": {"table": {"columns": {"name": "Project title", "dateEnd": "Closing Date", "lastUpdatedAt": "Last Updated", "actions": "Actions", "titulaire": "Researcher", "infrastructure": "Infrastructure"}}}, "description": {"manufacturiers": "Find information about manufacturers in our directory.", "etablissements": "Explore the list of establishments in our network.", "unites": "Discover the various units within our organization.", "personnes": "Search for individuals in our comprehensive directory.", "locaux": "Locate and get details about our local facilities.", "batiments": "View information about our buildings and structures.", "campus": "Explore our campus locations and facilities.", "projetsFinancement": "Learn about our current financing projects and initiatives."}, "form": {"sections": {"description": {"title": "Description", "contactDetails": {"addAddress": "Add an address", "title": "Contact details", "fields": {"addressType": {"label": "Contact type", "options": {"campus": "Campus", "civicAddress": "Civic address"}}, "phone": {"label": "Phone number", "error": {"max": "Phone number must be less than 100 characters"}}, "address": {"title": "Addresses", "civicAddress": {"title": "Civic address", "street": {"label": "Street", "error": {"required": "Street is required"}}, "city": {"label": "City"}}, "campus": {"title": "Campus", "listedLocal": {"label": "Listed local"}, "listedBuilding": {"label": "Listed building", "error": {"required": "Building is required"}}}}}, "addContact": "Add a phone number"}, "generalInfo": {"title": "General information", "fields": {"building": {"label": "Building", "error": {"required": "Building is required"}}, "jurisdiction": {"label": "Juris<PERSON>", "error": {"required": "Jurisdiction is required"}}, "name": {"label": "Name in {locale}", "error": {"required": "Name is required", "max": "Name must be less than {max} characters"}}, "type": {"label": "Type"}, "alias": {"label": "<PERSON>as in {locale}", "tooltip": "Other or former names", "error": {"max": "Alias must be less than {max} characters"}}, "pseudonym": {"label": "Pseudonym in {locale}", "tooltip": "Other or former names or numbers used to refer to the same local", "error": {"max": "Pseudonym must be less than {max} characters"}}, "description": {"label": "Description", "error": {"max": "Description must be less than 1000 characters"}}, "dateEnd": {"label": "Closing date"}, "establishmentType": {"label": "Type of establishment", "error": {"required": "Type of establishment is required"}}, "acronym": {"label": "Acronym", "error": {"max": "Acronym must be less than {max} characters"}}, "relatedOrganizations": {"title": "Related organizations"}, "parentUnit": {"label": "Parent unit", "error": {"required": "Parent unit is required"}}, "firstName": {"label": "First name", "error": {"required": "First name is required"}}, "lastName": {"label": "Last name", "error": {"required": "Last name is required"}}, "email": {"label": "Email", "addEmail": "Add an email address", "error": {"required": "At least one email address is required", "invalid": "Email address is invalid"}}, "phone": {"label": "Phone number", "addPhone": "Add a phone number", "error": {"required": "At least one phone number is required"}}, "localCapacity": {"label": "Local capacities", "tooltip": "Information related to building services requirements for equipment. This information allows moving management.", "options": {"area": "Actual net area (m²)", "capacity": "Bearing capacity of the floor (kgf/m²)"}}}}, "affiliationDetails": {"title": "Affiliation details", "fields": {"unit": {"label": "Unit", "error": {"required": "Unit is required"}}, "affiliationType": {"label": "Affiliation type", "options": {"infrastructure": "Infrastructure", "unit": "Unit", "establishment": "Establishment"}}, "infrastructure": {"label": "Infrastructure", "error": {"required": "Infrastructure is required"}}, "establishment": {"label": "Establishment", "error": {"required": "Establishment is required"}}, "firstName": {"label": "First name", "error": {"required": "First name is required"}}, "lastName": {"label": "Last name", "error": {"required": "Last name is required"}}, "email": {"label": "Email", "addEmail": "Add an email address", "error": {"required": "At least one email address is required"}}, "phone": {"label": "Phone number", "addPhone": "Add a phone number", "error": {"required": "At least one phone number is required"}}}}}, "manufacturer": {"generalInfo": {"fields": {"name": {"label": "Name in {locale}", "error": {"required": "Name is required", "max": "Name must be less than 100 characters"}}}}}, "unit": {"fields": {"name": {"label": "Name", "error": {"required": "Name is required"}}}}, "affiliations": {"title": "Affiliations", "affiliatedSection": {"title": "Affiliated organizations", "addAffiliatedField": "Add an affiliated organization"}, "affiliatedPersons": {"title": "Affiliated persons", "fields": {"jobTitle": {"label": "Job title", "error": {"required": "Job title is required"}}}}}, "person": {"fields": {"address": {"campus": {"error": {"required": "Building is required"}}, "civicAddress": {"error": {"required": "Building is required"}}}, "firstName": {"label": "First name", "error": {"required": "First name is required"}}, "lastName": {"label": "Last name", "error": {"required": "Last name is required"}}, "email": {"label": "Email", "addEmail": "Add an email address", "error": {"required": "At least one email address is required"}}, "phone": {"label": "Phone number", "addPhone": "Add a phone number", "error": {"required": "At least one phone number is required"}}}}}}}