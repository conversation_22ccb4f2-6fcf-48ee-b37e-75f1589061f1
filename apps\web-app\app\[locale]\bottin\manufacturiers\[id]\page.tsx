import type { BasePageParams } from '@/types/common';
import { setRequestLocale } from 'next-intl/server';

type InfrastructurePageParams = {
  params: {
    id: string;
  };
} & BasePageParams;

export default async function InfrastructurePage(
  props: InfrastructurePageParams,
) {
  const params = await props.params;

  const { locale } = params;

  setRequestLocale(locale);

  return (
    <>
      <div>HELLO FROM ID</div>
    </>
  );
}
