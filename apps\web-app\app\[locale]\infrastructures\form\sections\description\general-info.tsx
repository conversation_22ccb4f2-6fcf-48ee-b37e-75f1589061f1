import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { ToggleGroupFormField } from '@/app/[locale]/infrastructures/form/sections/description/components/ToggleGroupFormField';
import { CharacterCount } from '@/components/character-count/character-count';
import { FieldInfo } from '@/components/FieldInfo';
import { FormSubsection } from '@/components/form-subsection/form-subsection';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useGetControlledList } from '@/hooks/controlled-list/useControlledListsData';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { InfrastructureFormSchema } from '@/schemas/infrastructure-form-schema';
import { Textarea } from '@/ui/textarea';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

export const GeneralInfo = () => {
  const locale = useAvailableLocale();
  const tInfrastructures = useTranslations(
    'infrastructures.form.sections.description.generalInfo',
  );
  const tInfrastructureBase = useTranslations('infrastructures.form.sections');
  const tCommon = useTranslations('common');
  const { control, formState } = useFormContext<InfrastructureFormSchema>();
  const selectsData = useGetControlledList({
    controlledLists: ['infrastructureType', 'infrastructureStatus'],
    locale,
  });

  const nameFields = useTranslatedField(control, 'name');
  const acronymFields = useTranslatedField(control, 'acronym');
  const descriptionFields = useTranslatedField(control, 'description');

  const nameError = getFieldErrorMessage(formState.errors, 'name');
  const nameErrorMessage = nameError
    ? tInfrastructureBase(nameError)
    : undefined;
  const descriptionError = getFieldErrorMessage(
    formState.errors,
    'description',
  );
  const descriptionErrorMessage = descriptionError
    ? tInfrastructureBase(descriptionError)
    : undefined;

  return (
    <FormSubsection title={tInfrastructures('title')}>
      <FieldWithTranslations
        control={control}
        errorMessage={nameErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="name"
        fields={nameFields.fields}
        label={(locale) =>
          tInfrastructures('fields.name.label', { locale: tCommon(locale) })
        }
        maxLength={150}
        onAddTranslation={nameFields.handleAddTranslation}
        onRemoveTranslation={nameFields.handleRemoveTranslation}
        required={true}
      />
      <FieldWithTranslations
        control={control}
        fieldComponent={(field) => <Input {...field} />}
        fieldName="acronym"
        fields={acronymFields.fields}
        label={(locale) =>
          tInfrastructures('fields.acronym.label', { locale: tCommon(locale) })
        }
        maxLength={30}
        onAddTranslation={acronymFields.handleAddTranslation}
        onRemoveTranslation={acronymFields.handleRemoveTranslation}
      />
      <FormField
        control={control}
        name="alias"
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              htmlFor="alias"
              label={tInfrastructures('fields.alias.label')}
              tooltip={tInfrastructures('fields.alias.tooltip')}
            />
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
              <CharacterCount count={field.value?.length ?? 0} max={1000} />
            </FieldInfo>
          </FormItem>
        )}
      />
      <ToggleGroupFormField
        control={control}
        label={tInfrastructures('fields.infoType.label')}
        name="infoType"
        options={selectsData.infrastructureType ?? []}
        required
      />
      <FieldWithTranslations
        control={control}
        errorMessage={descriptionErrorMessage}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName="description"
        fields={descriptionFields.fields}
        label={(locale) =>
          tInfrastructures('fields.description.label', {
            locale: tCommon(locale),
          })
        }
        maxLength={2000}
        onAddTranslation={descriptionFields.handleAddTranslation}
        onRemoveTranslation={descriptionFields.handleRemoveTranslation}
      />
      <ToggleGroupFormField
        control={control}
        label={tInfrastructures('fields.status.label')}
        name="status"
        options={selectsData.infrastructureStatus ?? []}
        required
      />
    </FormSubsection>
  );
};
