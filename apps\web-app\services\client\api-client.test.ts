import { env } from '@/env';
import { server } from '@/mocks/server';
import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import { http, HttpResponse } from 'msw';
import { vi } from 'vitest';

// Mock the navigation module to prevent errors in the test environment
vi.mock('@/lib/navigation', () => ({
  redirect: vi.fn(),
}));

const testApiUrl = `${env.NEXT_PUBLIC_API_BASE_URL}/test`;
const sessionUrl = `${env.NEXT_PUBLIC_API_BASE_URL}/v2/auth/get-session`;

describe('api-client', () => {
  // Clear mocks before each test to ensure a clean state
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should retry the request and succeed when the session is valid', async () => {
    let requestCount = 0;
    server.use(
      http.get(testApiUrl, () => {
        requestCount++;
        // Fail the first time, succeed on the retry
        if (requestCount > 1) {
          return HttpResponse.json({ success: true });
        }
        return new HttpResponse(null, { status: 401 });
      }),
      // Mock a valid session
      http.get(sessionUrl, () => {
        return HttpResponse.json({
          session: { id: 'session-id', user: { id: 'user-id' } },
        });
      }),
    );

    const apiClient = await getApiClient();
    const response = await apiClient.get('test').json();

    expect(response).toEqual({ success: true });
    // The request should have been made twice (initial + retry)
    expect(requestCount).toBe(2);
  });

  it('should fail without retrying when the session is invalid', async () => {
    let requestCount = 0;
    server.use(
      http.get(testApiUrl, () => {
        requestCount++;
        return new HttpResponse(null, { status: 401 });
      }),
      // Mock an invalid session
      http.get(sessionUrl, () => {
        return new HttpResponse(null, { status: 401 });
      }),
    );

    const apiClient = await getApiClient();

    // The promise should reject with a custom APIV2Error because the session refresh fails.
    await expect(apiClient.get('test')).rejects.toThrow(APIV2Error);
    // The request should only be made once
    expect(requestCount).toBe(1);
  });
});
