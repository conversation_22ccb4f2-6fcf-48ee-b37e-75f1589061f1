import { useEffect, useState } from 'react';

export const usePagination = (queryParams: string) => {
  const [{ pageIndex, pageSize }, setPagination] = useState({
    pageIndex: 0,
    pageSize: 50,
  });

  // biome-ignore lint/correctness/useExhaustiveDependencies: <Whenever queryParams change we need to update the pagination>
  useEffect(() => {
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, [queryParams]);

  return {
    pageIndex,
    pageSize,
    setPagination,
  };
};
