'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Calendar, type CalendarProps } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { type DateTimeFormatOptions, useFormatter } from 'next-intl';
import * as React from 'react';
import { CiCalendar } from 'react-icons/ci';

type DatePickerProps = {
  formatOptions?: DateTimeFormatOptions;
  mode: 'single';
  onSelect: (selectedDate: Date | undefined) => void;
  placeholder: string;
  selected: Date | undefined;
} & CalendarProps;

export const DatePicker = ({
  className,
  formatOptions = { day: 'numeric', month: 'long', year: 'numeric' },
  mode = 'single',
  onSelect,
  placeholder,
  selected,
  ...calendarProps
}: DatePickerProps) => {
  const formatter = useFormatter();

  const renderDateLabel = () => {
    return selected ? formatter.dateTime(selected, formatOptions) : placeholder;
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          data-testid="datepicker-trigger"
          className={cn(
            'w-[280px] justify-start text-left font-normal',
            !selected && 'text-muted-foreground',
            className,
          )}
          variant={'outline'}
        >
          <CiCalendar className="mr-2 h-4 w-4" />

          {renderDateLabel()}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0">
        <Calendar
          initialFocus
          mode={mode}
          onSelect={onSelect}
          selected={selected}
          {...calendarProps}
        />
      </PopoverContent>
    </Popover>
  );
};
