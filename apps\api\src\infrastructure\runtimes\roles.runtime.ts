import { PgDatabaseLayer } from '@rie/postgres-db';
import { RolesRepositoryLive } from '@rie/repositories';
import { RolesServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const RoleServicesLayer = Layer.mergeAll(
  RolesRepositoryLive.Default,
  RolesServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const RolesRuntime = ManagedRuntime.make(RoleServicesLayer);
