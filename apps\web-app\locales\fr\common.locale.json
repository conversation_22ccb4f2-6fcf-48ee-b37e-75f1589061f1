{"About": "À propos", "Data": "Données du RIÉ", "Decision tools": "Outils d'aide à la décision", "Eco responsibility": "Écoresponsabilité", "Fast Access": "Accès rapide", "Glossary": "Glossaire", "How to use RIE": "Comment utiliser RIÉ ?", "News": "Actualités", "location": "Emplacement", "manufacturer": "Manufacturier", "Registry": "Consulter le registre", "Resources": "Ressources", "actions": {"created": "<PERSON><PERSON><PERSON>", "deleted": "Supprimé", "deletedFeminine": "Supprimée", "updated": "Mis à jour"}, "add": "Ajouter", "addEquipment": "Ajouter un équipement", "addResource": "Ajouter {resource}", "addTranslation": "Ajouter une traduction", "applyResource": "Appliquer {resource}", "backHome": "Retour accueil", "backToResults": "Retour aux résultats", "buildingsCount": "{count, plural, =0 {Aucun bâtiment accessible} =1 {# bâtiment accessible} other {# bâtiments accessibles}}", "by": "par", "campusCount": "{count, plural, =0 {Aucun campus accessible} =1 {# campus accessible} other {# campus accessibles}}", "cancel": "Annuler", "categoryPlural": "{count, plural, =0 {} =1 {Caté<PERSON><PERSON>} other {Catégories}}", "columns": "Colonnes", "components": {"address": {"title": "<PERSON><PERSON><PERSON>", "addressType": {"label": "Type de contact", "campus": "Campus", "civicAddress": "Adresse civique"}, "campus": {"title": "Adresse dans le campus", "building": {"label": "Bâtiment", "placeholder": "Sé<PERSON><PERSON>ner un bâtiment", "error": {"required": "Veuillez sélectionner un bâtiment"}}, "room": {"label": "Local", "placeholder": "Sélectionner un local", "awaitingForBuildingSelection": "Sélectionnez un bâtiment pour pouvoir sélectionner un local", "noRoomsAssociatedWithBuilding": "Il n'y a pas de locaux associés au bâtiment sélectionné"}}, "civicAddress": {"label": "<PERSON><PERSON><PERSON>", "error": {"required": "<PERSON><PERSON><PERSON><PERSON> saisir une adresse"}}, "addAddress": "Ajouter une adresse"}}, "contract": "Contrat", "count": "Conteur est à", "createdAt": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleteItemQuestion": "Est-vous sur de vouloir supprimer {item}?", "deleteResource": "Supprimer {resource}", "description": "Description", "directory": {"manufacturers": "manufacturiers", "establishments": "établissements", "units": "unités", "people": "personnes", "rooms": "locaux", "buildings": "bâtiments", "campus": "campus", "financingProjects": "projets de financement"}, "document": "Document", "edit": "É<PERSON>er", "en": "<PERSON><PERSON><PERSON>", "equipment": "Équipement", "equipmentCount": "{count, plural, =0 {Aucun équipement accessible} =1 {# équipement accessible} other {# équipements accessibles}}", "equipments": "Équipements", "establishmentsCount": "{count, plural, =0 {Aucun établissement accessible} =1 {# établissement accessible} other {# établissements accessibles}}", "file": "<PERSON><PERSON><PERSON>", "files": "Documents", "filterResults": "Filtrer les résultats", "filters": "Filtres", "financingProjectsCount": "{count, plural, =0 {Aucun projet de financement accessible} =1 {# projet de financement accessible} other {# projets de financement accessibles}}", "fr": "Français", "fundingProjectsCount": "{count, plural, =0 {Aucun projet de financement accessible} =1 {# projet de financement accessible} other {# projets de financement accessibles}}", "goTo": "<PERSON><PERSON>", "home": "Accueil", "image": "Image", "images": "Images", "infrastructure": "Infrastructure", "infrastructureCount": "{count, plural, =0 {Aucune infrastructure accessible} =1 {# infrastructure accessible} other {# infrastructures accessibles}}", "infrastructures": "Infrastructures", "languageSwitcher": {"label": "Sélectionner la langue"}, "loading": "Chargement en cours...", "manufacturersCount": "{count, plural, =0 {Aucun manufacturier accessible} =1 {# manufacturier accessible} other {# manufacturiers accessibles}}", "missing": "Non renseigné ({count})", "missingSelected": {"infrastructure": "Infrastructure non renseignée", "equipment": "Équipement non renseigné", "manufacturer": "Manufacturier non renseigné", "establishment": "Établissement non renseigné", "unit": "Unité non renseignée", "person": "Personne non renseignée", "room": "Local non renseigné", "building": "Bâtiment non renseigné", "campus": "Campus non renseigné", "contract": "Contrat non renseigné", "financingProject": "Projet de financement non renseigné", "default": "Non renseigné", "category": "Catégorie non renseignée", "researchDomain": "Domaine de recherche non renseigné", "supplier": "Fournisseur non renseigné", "technique": "Technique non renseignée", "poleExcellences": "Pôle d'excellence non renseigné", "SecteurApplications": "Secteur d'application non renseigné"}, "model": "<PERSON><PERSON><PERSON><PERSON>", "noResults": "Pas de r<PERSON>", "notifications": {"errors": {"generic": "Une erreur s'est produite.", "onAction": "<PERSON><PERSON>ur lors de la {action} dans {resource}", "onFetchResource": "Erreur lors de l'obtention de la resource {resource}.", "onSaveResource": "Erreur lors de la sauvegarde de la resource {resource}.", "onDeleteResource": "Erreur lors de la suppression de la resource {resource}.", "onCreateResource": "Erreur lors de la création de la resource {resource}.", "onUpdateResource": "Erreur lors de la mise à jour de la resource {resource}."}, "successTitle": "Su<PERSON>ès", "errorTitle": "<PERSON><PERSON><PERSON>", "success": "{resource} {action} avec succès.."}, "ok": "OK", "operations": {"create": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "update": "É<PERSON>er", "read": "Voir", "fetch": "<PERSON><PERSON><PERSON><PERSON>"}, "page": "Page", "pagination": {"goToPage": "Go to page", "rowsPerPage": "Lignes par page", "page": "Page", "of": "de", "go": "<PERSON><PERSON>"}, "peopleCount": "{count, plural, =0 {Aucune personne accessible} =1 {# personne accessible} other {# personnes accessibles}}", "permanentDeleteAction": "Cette action est permanente et ne peut pas être annulée.", "pickDate": "Choisir une date", "roomsCount": "{count, plural, =0 {Aucun local accessible} =1 {# local accessible} other {# locaux accessibles}}", "removeTranslation": "Supprimer traduction", "resetResource": "Réinitialiser les filtres", "resources": {"building": "Bâtiment", "buildings": "Bâtiments", "directory": "<PERSON><PERSON><PERSON>", "equipment": "Équipement", "equipments": "Équipements", "infrastructure": "Infrastructure", "infrastructures": "Infrastructures", "manufacturer": "Manufacturier", "manufacturers": "Manufacturiers", "establishment": "Établissement", "establishments": "Établissements", "unit": "Unité", "units": "Unités", "room": "Local", "rooms": "Locals", "people": "<PERSON><PERSON>", "peoples": "<PERSON><PERSON>", "contract": "Contract", "contracts": "Contracts", "campus": "Campus", "campuses": "Campuses", "financingProject": "Projet de financement", "financingProjects": "Projets de financement", "controlledList": {"technique": "Technique"}}, "save": "<PERSON><PERSON><PERSON><PERSON>", "saving": "Sauvegarde en cours", "search": "<PERSON><PERSON><PERSON>", "searchEquipment": "Chercher un équipement", "searchInfrastructure": "Chercher une infrastructure", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectEntityToEnable": "Sélectionner {entity} pour activer ce champs", "show": "<PERSON><PERSON><PERSON><PERSON>", "sort": {"sortBy": "Trier par", "relevance": "Pertinence", "mostRecent": "Plus récent", "lessRecent": "<PERSON><PERSON> r<PERSON>"}, "unitsCount": "{count, plural, =0 {Aucune unité accessible} =1 {# unité accessible} other {# unités accessibles}}", "update": "Mettre à jour", "updatedAt": "Mis à jour le", "url": "URL", "video": "Vidéo", "videos": "Vid<PERSON><PERSON>", "view": "Voir", "viewAllResources": "Toutes les {resources}", "viewOwnResources": "Uniquement mes {resources}", "category": "<PERSON><PERSON><PERSON><PERSON>"}