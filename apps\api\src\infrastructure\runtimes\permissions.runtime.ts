import { PgDatabaseLayer } from '@rie/postgres-db';
import { PermissionsRepositoryLive } from '@rie/repositories';
import { PermissionsServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PermissionServicesLayer = Layer.mergeAll(
  PermissionsRepositoryLive.Default,
  PermissionsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const PermissionsRuntime = ManagedRuntime.make(PermissionServicesLayer);
