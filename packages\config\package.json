{"name": "@rie/config", "version": "1.0.0", "type": "module", "description": "Config package", "main": "./build/index.js", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "exports": {".": {"types": "./build/dts/index.d.ts", "import": "./build/index.js", "require": "./build/index.js"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json && tsc-alias -p tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w && tsc-alias -p tsconfig.build.json", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write"}, "devDependencies": {"tsc-alias": "^1.8.16", "typescript": "^5.8.3"}, "peerDependencies": {"effect": "^3.16.9"}}