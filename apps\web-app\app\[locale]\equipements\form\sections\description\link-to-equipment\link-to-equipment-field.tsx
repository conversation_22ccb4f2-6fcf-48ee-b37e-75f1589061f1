import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { ComboboxFieldInfiniteScroll } from '@/components/form-fields/combobox-field-infinite-scroll';
import { Textarea } from '@/components/ui/textarea';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

type LinkToEquipmentProps = {
  equipements: { label: string; value: string }[];
  fetchNextPage: () => void;
  hasNextPage: boolean;
  index: number;
  isFetching: boolean;
  isFetchingNextPage: boolean;
  onSearchChange: (searchTerm: string) => void;
};

export const LinkToEquipmentField = ({
  equipements,
  fetchNextPage,
  hasNextPage,
  index,
  isFetching,
  isFetchingNextPage,
  onSearchChange: setSearchTerm,
}: LinkToEquipmentProps) => {
  const tCommon = useTranslations('common');
  const tEquipments = useTranslations(
    'equipments.form.sections.description.linkToEquipment',
  );
  const { control, formState } = useFormContext<EquipmentFormSchema>();
  const descriptionFields = useTranslatedField(
    control,
    `otherEquipments.${index}.description`,
  );

  const descriptionError = getFieldErrorMessage(
    formState.errors,
    `otherEquipments.${index}.description`,
  );
  const descriptionErrorMessage = descriptionError
    ? tEquipments(descriptionError)
    : undefined;

  return (
    <>
      <ComboboxFieldInfiniteScroll
        clearErrorsOnChange={false}
        fetchNextPage={fetchNextPage}
        fieldLabel={tEquipments(
          'fields.otherEquipments.fields.equipment.label',
        )}
        fieldName={`otherEquipments.${index}.equipment`}
        hasNextPage={hasNextPage}
        isFetching={isFetching}
        isFetchingNextPage={isFetchingNextPage}
        onSearchChange={setSearchTerm}
        options={equipements ?? []}
        placeholder={tEquipments(
          'fields.otherEquipments.fields.equipment.placeholder',
        )}
        required
      />
      <FieldWithTranslations
        control={control}
        errorMessage={descriptionErrorMessage}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName={`otherEquipments.${index}.description`}
        fields={descriptionFields.fields}
        label={(locale) =>
          tEquipments(
            'fields.otherEquipments.fields.description.labelWithLocale',
            {
              locale: tCommon(locale),
            },
          )
        }
        maxLength={1000}
        onAddTranslation={descriptionFields.handleAddTranslation}
        onRemoveTranslation={descriptionFields.handleRemoveTranslation}
      />
    </>
  );
};
