import type { DirectoryEditPathnames } from '@/i18n/settings';
import type { DirectoryEntity } from '@rie/domain/types';
import { mapFacetFullToFacet } from '@/services/mappers/map-facets-to-filters';
import type {
  BaseEntity,
  Building,
  Campus,
  DirectoryFull,
  Entity,
  EntityResponseMap,
  EntityTypeMap,
  Establishment,
  FundingProject,
  Location,
  Manufacturer,
  Person,
  Unit,
} from '@/types/bottin/directory';
import type { ApiReturnType, MapperReturnType } from '@/types/common';
import type { PersonFull, ProviderFull } from '@/types/controlled-list';
import type { FacetValue } from '@/types/filters';

type EntityMapper<T extends DirectoryFull, U extends Entity> = (entity: T) => U;

const directoryEntityMap: {
  [K in DirectoryEntity]: EntityMapper<EntityResponseMap[K], EntityTypeMap[K]>;
} = {
  building: (entity): Building => ({
    ...getBaseFields(entity),
    campus: entity.campus.text,
    jurisdiction: entity.juridiction.text,
    nom: entity.nom,
  }),
  campus: (entity): Campus => ({
    ...getBaseFields(entity),
    jurisdiction: entity.etablissement.text,
  }),
  institution: (entity): Establishment => ({
    ...getBaseFields(entity),
    acronym: entity.acronym,
    establishmentType: entity.typeEtablissement?.text,
  }),
  fundingProject: (entity): FundingProject => ({
    ...getBaseFields(entity),
    // infrastructure: entity.infrastructure.text,
    titreProjet: entity.text,
    titulaire: entity.titulaire.text,
  }),
  vendor: (entity): Manufacturer => ({
    ...getBaseFields(entity),
    dateEnd: entity.dateEnd,
    emplacements: entity.emplacements,
    telephones: entity.telephones,
  }),
  people: (entity): Person => ({
    email: entity.emails ? entity.emails.map((email) => email.address) : [],
    firstName: entity.givenName,
    id: typeof entity.id === 'number' ? entity.id.toString() : entity.id,
    lastName: entity.familyName,
    lastUpdatedAt: entity.lastUpdatedAt,
    text: entity.text,
    uid: entity.uid,
  }),
  room: (entity): Location => ({
    ...getBaseFields(entity),
    building: entity.batiment.text,
    jurisdiction: entity.juridiction.text,
    numero: entity.numero,
  }),
  unit: (entity): Unit => ({
    ...getBaseFields(entity),
    acronym: entity.acronym,
    organizationId: entity.organizationId,
    parentName: entity.parent?.text,
  }),
};

const getBaseFields = (
  entity: Exclude<DirectoryFull, PersonFull>,
): BaseEntity => ({
  createdAt: entity.createdAt,
  id: typeof entity.id === 'number' ? entity.id.toString() : entity.id,
  lastUpdatedAt: entity.lastUpdatedAt,
  text: entity.text,
  uid: entity.uid,
});

export const mapEntitiesToTableColumns = <
  T extends DirectoryFull,
  U extends Entity,
>(
  { count, data, facets }: ApiReturnType<T[]>,
  entityType: DirectoryEntity,
): MapperReturnType<U[], FacetValue[]> => {
  const mapper = directoryEntityMap[entityType] as EntityMapper<T, U>;
  return {
    count,
    data: data.map(mapper),
    facets: mapFacetFullToFacet(facets),
  };
};

export const mapEntityToTableColumns = <
  T extends DirectoryFull,
  U extends Entity,
>(
  entity: T,
  directoryEntity: DirectoryEntity,
): U => {
  const mapper = directoryEntityMap[directoryEntity] as EntityMapper<T, U>;
  return mapper(entity);
};

// Fonctions spécifiques pour chaque type d'entité
export const mapManufacturersToTableColumns = (
  data: ApiReturnType<ProviderFull[]>,
): MapperReturnType<Manufacturer[], FacetValue[]> => {
  return mapEntitiesToTableColumns<ProviderFull, Manufacturer>(data, 'vendor');
};

// export const mapBuildingsToTableColumns = (
//   data: ApiReturnType<BuildingFull[]>
// ): MapperReturnType<Building[], FacetValue[]> => {
//   return mapEntitiesToTableColumns<BuildingFull, Building>(data, 'buildings');
// };

// Ajoutez des fonctions similaires pour les autres types d'entités si nécessaire

// Fonction générique pour mapper n'importe quel type d'entité
// export const mapAnyEntityToTableColumns = <T extends EntityFull, U extends Entity>(
//   data: ApiReturnType<T[]>,
//   entityType: EntityType
// ): MapperReturnType<U[], FacetValue[]> => {
//   return mapEntitiesToTableColumns<T, U>(data, entityType);
// };

export const directoryEntityPathMap = (
  directoryEntity: DirectoryEntity,
): string => {
  const routeMap: Record<DirectoryEntity, string> = {
    building: 'batiment',
    campus: 'campus',
    institution: 'etablissement',
    fundingProject: 'projet-financement',
    vendor: 'fournisseur',
    people: 'person',
    room: 'local',
    unit: 'unite',
  };

  return routeMap[directoryEntity] || directoryEntity;
};

export const mapEntitiesToRouteWithLocale = (
  directoryEntity: DirectoryEntity,
): DirectoryEditPathnames => {
  const routeMap: Record<DirectoryEntity, DirectoryEditPathnames> = {
    building: '/bottin/batiments/[id]/editer',
    campus: '/bottin/campus/[id]/editer',
    institution: '/bottin/etablissements/[id]/editer',
    fundingProject: '/bottin/projets-financement/[id]/editer',
    vendor: '/bottin/manufacturiers/[id]/editer',
    people: '/bottin/personnes/[id]/editer',
    room: '/bottin/locaux/[id]/editer',
    unit: '/bottin/unites/[id]/editer',
  };

  return routeMap[directoryEntity];
};
