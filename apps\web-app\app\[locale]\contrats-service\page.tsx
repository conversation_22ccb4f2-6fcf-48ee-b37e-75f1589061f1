import { ServiceContractsPage } from '@/app/[locale]/contrats-service/contract-service-page';
import { RouteGuard } from '@/components/permissions/route-guard';
import { getQueryClientOptions } from '@/constants/query-client';
import { allServiceContractOptions } from '@/hooks/contract-service/useGetAllServiceContracts';
import type { BasePageParams } from '@/types/common';
import {
  dehydrate,
  HydrationBoundary,
  QueryClient,
} from '@tanstack/react-query';
import { notFound } from 'next/navigation';

export default async function ContractServicesPage(props: BasePageParams) {
  const params = await props.params;

  const { locale } = params;

  // TODO: For the time being we are not using this page in production,
  //  once we are ready to deploy it, we'll remove the notFound()
  if (process.env.NODE_ENV === 'production') {
    return notFound();
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  await queryClient.prefetchQuery(allServiceContractOptions(locale));

  return (
    <RouteGuard operation="read" resource="serviceContract">
      <HydrationBoundary state={dehydrate(queryClient)}>
        <ServiceContractsPage locale={locale} />
      </HydrationBoundary>
    </RouteGuard>
  );
}
