import { DocumentSourceItem } from '@/app/[locale]/equipements/form/components/document-source/document-source-item';
import { InputField } from '@/components/form-fields/input-field';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { UploadFile } from '@/components/upload-file/upload-file';
import { IMAGE_MAX_UPLOAD_SIZE_MB } from '@/constants/equipments';
import type { MediaSourceType } from '@/types/media';
import { FormControl, FormField, FormItem } from '@/ui/form';
import { ToggleGroup, ToggleGroupItem } from '@/ui/toggle-group';
import { useTranslations } from 'next-intl';
import {
  type Control,
  type FieldValues,
  type Path,
  useFormContext,
} from 'react-hook-form';

type MediaSourceProps<TFieldData extends FieldValues> = {
  control: Control<TFieldData>;
  fieldName: Path<TFieldData>;
  source: MediaSourceType;
};

export const MediaSourceItem = <TFieldData extends FieldValues>({
  control,
  fieldName,
  source,
}: MediaSourceProps<TFieldData>) => {
  const tCommon = useTranslations('common');
  const tEquipments = useTranslations('equipments.form.sections.description');
  const { clearErrors, watch } = useFormContext<TFieldData>();
  const fileSource = watch(fieldName);

  if (source === 'documents') {
    return (
      <DocumentSourceItem
        control={control}
        fieldName={fieldName}
        source={source}
      />
    );
  }

  return (
    <div className="grid gap-y-4">
      <FormField
        control={control}
        name={fieldName}
        render={({ field }) => (
          <FormItem className="w-fit">
            <LabelTooltip htmlFor={fieldName} label="" />
            <FormControl>
              <ToggleGroup
                onValueChange={(value) => {
                  if (value !== '') {
                    field.onChange({ data: undefined, type: value });
                    clearErrors(fieldName);
                  }
                }}
                type="single"
                value={field.value.type}
                variant="outline"
              >
                {source === 'videos' ? (
                  <>
                    <ToggleGroupItem value="url">
                      {tCommon('url')}
                    </ToggleGroupItem>
                  </>
                ) : (
                  <>
                    <ToggleGroupItem value="file">
                      {tCommon('file')}
                    </ToggleGroupItem>
                    <ToggleGroupItem value="url">
                      {tCommon('url')}
                    </ToggleGroupItem>
                  </>
                )}
              </ToggleGroup>
            </FormControl>
          </FormItem>
        )}
      />

      <div className="grid w-full gap-y-4">
        {source === 'videos' ? (
          <InputField
            className="w-full pb-6"
            fieldName={`${fieldName}.data` as Path<TFieldData>}
            label={tCommon('url')}
          />
        ) : fileSource.type === 'file' ? (
          <UploadFile
            description={tEquipments(`media.fields.${source}.fileSize`, {
              size: IMAGE_MAX_UPLOAD_SIZE_MB,
            })}
            fieldName={`${fieldName}.data` as Path<TFieldData>}
            label={
              fileSource.data?.name ??
              tEquipments(`media.fields.${source}.upload`)
            }
            source={source}
          />
        ) : (
          <InputField
            className="w-full pb-6"
            fieldName={`${fieldName}.data` as Path<TFieldData>}
            label={tCommon('url')}
          />
        )}
      </div>
    </div>
  );
};
