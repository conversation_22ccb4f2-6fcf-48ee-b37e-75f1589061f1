import {
  civicCampusAddressSchema,
  selectOptionSchema,
  selectedOptionSchemaArray,
} from '@/schemas/common-schema';
import { z } from 'zod';

export const getEquipmentGeneralInfoFormSchema = (
  t: (val: string, args?: Record<string, number | string>) => string,
) => {
  const nameSchema = z
    .array(
      z.object({
        locale: z.string().trim(),
        value: z
          .string()
          .trim()
          .max(150, {
            message: t('description.generalInfo.fields.name.error.max', {
              max: 150,
            }),
          }),
      }),
    )
    .refine((data) => data.some(({ value }) => value !== ''), {
      message: t('description.generalInfo.fields.name.error.required'),
    });

  const descriptionSchema = z.array(
    z.object({
      locale: z.string().trim(),
      value: z
        .string()
        .trim()
        .max(1500, {
          message: t('description.generalInfo.fields.description.error.max', {
            max: 1500,
          }),
        }),
    }),
  );

  const manufacturerProviderSchema = z
    .object({
      manufacturer: selectOptionSchema(),
      supplier: selectOptionSchema(),
    })
    .superRefine((data, ctx) => {
      if (!data.manufacturer && !data.supplier) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          fatal: true,
          message: t(
            'description.generalInfo.fields.manufacturerProvider.error.required',
          ),
        });

        return z.NEVER;
      }
    });

  return z.object({
    breakdown: z
      .string()
      .trim()
      .max(1000, {
        message: t('description.generalInfo.fields.breakdown.error.max', {
          max: 1000,
        }),
      })
      .optional(),
    address: civicCampusAddressSchema({
      buildingAddressErrorMessage: t(
        'description.generalInfo.fields.building.error.required',
      ),
    }),
    categories: selectedOptionSchemaArray.min(1, {
      message: t('description.generalInfo.fields.categories.error.required'),
    }),
    description: descriptionSchema,
    equipmentHolder: selectOptionSchema(
      t('description.generalInfo.fields.equipmentHolder.error.required'),
    ),
    inventoryNumber: z
      .string()
      .trim()
      .max(20, {
        message: t('description.generalInfo.fields.inventoryNumber.error.max', {
          max: 20,
        }),
      }),
    jurisdiction: selectOptionSchema(
      t('description.generalInfo.fields.jurisdiction.error.required'),
    ),
    manufacturerProvider: manufacturerProviderSchema,
    model: z
      .string()
      .trim()
      .max(90, {
        message: t('description.generalInfo.fields.model.error.max', {
          max: 90,
        }),
      }),
    name: nameSchema,
    percentageFunctionality: z
      .number()
      .int()
      .min(0, {
        message: t(
          'description.generalInfo.fields.percentageFunctionality.error.min',
        ),
      })
      .max(100, {
        message: t(
          'description.generalInfo.fields.percentageFunctionality.error.max',
          {
            max: 100,
          },
        ),
      })
      .or(z.string().regex(/^\d+$/).transform(Number))
      .refine((val) => val >= 0 && val <= 100, {
        message: t(
          'description.generalInfo.fields.percentageFunctionality.error.invalid',
        ),
      })
      .optional(),

    serialNumber: z
      .string()
      .trim()
      .max(40, {
        message: t('description.generalInfo.fields.serialNumber.error.max', {
          max: 40,
        }),
      }),

    status: selectOptionSchema(
      t('description.generalInfo.fields.status.error.required'),
    ),

    type: selectOptionSchema(
      t('description.generalInfo.fields.type.error.required'),
    ),
  });
};
