import { PermissionGate } from '@/components/permissions/permission-gate';
import { Button, buttonVariants } from '@/components/ui/button';
import type { AppEditPathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import { cn } from '@/lib/utils';
import type { ResourceType } from '@rie/domain/types';
import { useTranslations } from 'next-intl';

type EditButtonProps = {
  equipmentId?: string;
  id: string;
  infrastructureId?: string;
  pathname: AppEditPathnames;
  resource: ResourceType;
};

export const EditButton = ({ id, pathname, resource }: EditButtonProps) => {
  const t = useTranslations('common');
  return (
    <PermissionGate resourceId={id} action="update" resourceType={resource}>
      <Button asChild>
        <Link
          className={cn(buttonVariants({ variant: 'default' }), 'sm')}
          href={{
            params: { id },
            pathname: pathname,
          }}
        >
          {t('edit')}
        </Link>
      </Button>
    </PermissionGate>
  );
};
