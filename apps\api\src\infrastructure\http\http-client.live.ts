import {
  FetchHttpClient,
  HttpClient,
  HttpClientRequest,
} from '@effect/platform';
import { ConfigLive } from '@rie/config';
import * as Effect from 'effect/Effect';
import * as Redacted from 'effect/Redacted';

export class ApiHttpClientLive extends Effect.Service<ApiHttpClientLive>()(
  'ApiHttpClientLive',
  {
    dependencies: [FetchHttpClient.layer, ConfigLive.Default],
    effect: Effect.gen(function* (_) {
      const makeHttpClient = Effect.gen(function* () {
        const defaultClient = yield* HttpClient.HttpClient;

        return defaultClient.pipe(
          HttpClient.mapRequestEffect((request) =>
            Effect.gen(function* () {
              const { NEXT_PUBLIC_API_BASE_URL } = yield* ConfigLive;

              return request.pipe(
                HttpClientRequest.prependUrl(
                  Redacted.value(NEXT_PUBLIC_API_BASE_URL),
                ),
                HttpClientRequest.setHeader('Content-Type', 'application/json'),
              );
            }),
          ),
          HttpClient.filterStatusOk,
        );
      });

      return { makeHttpClient } as const;
    }),
  },
) {}
