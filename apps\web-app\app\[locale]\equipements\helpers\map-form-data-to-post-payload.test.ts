import { NULLABLE_SELECT_OPTION } from '@/constants/common';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import { describe, expect, it } from 'vitest';

import {
  getLifespanOrMaxMaintenance,
  mapFormDataToPostPayload,
} from './map-form-data-to-post-payload';

describe('getLifespanOrMaxMaintenance', () => {
  it('it should return an object with "unite" and "valuer" keys when value is a positive number', () => {
    const result = getLifespanOrMaxMaintenance(
      '3',
      { label: 'label', value: 'value' },
      'equipementDureeVie',
    );

    expect(result).toStrictEqual({
      equipementDureeVie: {
        unite: { id: 'value' },
        valuer: 3,
      },
    });
  });

  it('it should return an empty object when value is a an empty string', () => {
    const result = getLifespanOrMaxMaintenance(
      '',
      { label: 'label', value: 'value' },
      'equipementDureeVie',
    );

    expect(result).toStrictEqual({});
  });

  it('it should return an empty object when value is "0"', () => {
    const result = getLifespanOrMaxMaintenance('', null, 'equipementDureeVie');

    expect(result).toStrictEqual({});
  });
});

describe('mapFormDataToPostPayload', () => {
  it('should map form data to post minimal payload correctly', () => {
    const formData: EquipmentFormSchema = {
      accessories: [],
      acquisitionCostInCash: '',
      acquisitionCostInNature: '',
      associatedFinancingProjects: [],
      address: {
        addressType: 'campus',
        data: {
          listedBuilding: { label: 'building 1', value: '1' },
          listedLocal: NULLABLE_SELECT_OPTION,
        },
      },
      categories: [{ label: 'test', value: '1' }],
      comments: [],
      components: [],
      depth: '',
      description: [],
      documents: [],
      DOIOfArticles: '',
      equipmentHolder: { label: 'equipmentHolder', value: '1' },
      equipmentUnit: NULLABLE_SELECT_OPTION,
      estimatedLifespan: '',
      excellencePole: [],
      height: '',
      hideEquipment: false,
      highlight: false,
      images: [],
      immaterialEquipment: [],
      infrastructure: NULLABLE_SELECT_OPTION,
      inventoryNumber: '',
      isSafe: false,
      jurisdiction: { label: 'jurisdiction', value: '1' },
      manufacturerProvider: {
        manufacturer: NULLABLE_SELECT_OPTION,
        supplier: NULLABLE_SELECT_OPTION,
      },
      maximalMaintenanceLength: '',
      measurements: [],
      minimalMaintenanceRequired: [],
      model: '',
      name: [],
      otherEquipments: [],
      registrationNumber: '',
      repairers: [],
      researchFields: [],
      serialNumber: '',
      servicesContract: [],
      siteDetails: [],
      socioEconomicObjectives: [],
      specifications: [],
      sstManager: [],
      sstRisks: [],
      status: NULLABLE_SELECT_OPTION,
      technicalManager: [],
      technics: [{ label: 'test', value: '1' }],
      type: { label: 'type', value: '1' },
      unit: NULLABLE_SELECT_OPTION,
      usageContexts: [],
      videos: [],
      weight: '',
      width: '',
    };

    const expectedPayload = {
      categories: {
        key: 'categories[0][id]',
        value: '1',
      },
      hideEquipment: {
        key: 'estMasquer',
        value: 'false',
      },
      jurisdiction: {
        key: 'juridiction[id]',
        value: '1',
      },
      highlight: {
        key: 'mettreEnVedette',
        value: 'false',
      },
      buildingLocation: {
        key: 'equipementEmplacement[batiment][id]',
        value: '1',
      },
      roomLocation: {
        key: 'equipementEmplacement[local][id]',
        value: 'null',
      },
      isLocal: {
        key: 'equipementEmplacement[estLocal]',
        value: 'true',
      },
      infrastructure: {
        key: 'infrastructure[id]',
        value: 'null',
      },
      techniques: {
        key: 'techniques[0][id]',
        value: '1',
      },
      manufacturer: {
        key: 'manufacturier[id]',
        value: 'null',
      },
      titulaire: {
        key: 'titulaire[id]',
        value: 'null',
      },
      equipmentType: {
        key: 'typeEquipement[id]',
        value: '1',
      },
      equipmentStatus: {
        key: 'etatEquipement[id]',
        value: 'null',
      },
      provider: {
        key: 'fournisseur[id]',
        value: 'null',
      },
    };

    const result = mapFormDataToPostPayload(formData);

    const keys = Object.values(expectedPayload).map(({ key }) => key);

    expect(Array.from(result.keys()).toSorted()).toEqual(keys.toSorted());
  });
});

//exemple de payload complet:
// const examplePayload: EquipmentPostPayload = {
//   anneeFabrication: 2022,
//   categories: [{ id: 'cat1' }, { id: 'cat2' }],
//   commentaires: {
//     en: 'Comments in English',
//     fr: 'Commentaires en français',
//   },
//   coutEspece: 10000,
//   coutNature: 5000,
//   dateAchat: '2023-01-15T00:00:00.000Z',
//   dateInstallation: '2023-02-01T00:00:00.000Z',
//   descriptions: {
//     en: 'Equipment description in English',
//     fr: 'Description de l\'équipement en français',
//   },
//   doi: '10.1234/example.doi',
//   equipementAccessoires: [{ id: 'acc1' }, { id: 'acc2' }],
//   equipementAutres: [
//     {
//       descriptions: {
//         en: 'Other equipment 1',
//         fr: 'Autre équipement 1',
//       },
//       entite: { id: 'other1' },
//       nature: { id: 'nature1' },
//     },
//   ],
//   equipementComposantes: [{ id: 'comp1' }, { id: 'comp2' }],
//   equipementDimension: {
//     hauteur: 100,
//     id: 'dim1',
//     largeur: 50,
//     masse: 200,
//     profondeur: 75,
//   },
//   equipementDureeVie: {
//     id: 'life1',
//     unite: { id: 'year' },
//     valeur: 10,
//   },
//   estMasquer: false,
//   etatEquipement: { id: 'state1' },
//   fournisseur: { id: 'supp1' },
//   frequenceMaintenance: {
//     id: 'freq1',
//     unite: { id: 'month' },
//     valeur: 6,
//   },
//   infrastructure: { id: 'infra1' },
//   juridiction: { id: 'jur1' },
//   maintenances: {
//     en: 'Maintenance requirements in English',
//     fr: 'Exigences de maintenance en français',
//   },
//   manufacturier: { id: 'manu1' },
//   mettreEnVedette: true,
//   modele: 'Model XYZ',
//   noms: {
//     en: 'Equipment Name',
//     fr: 'Nom de l\'équipement',
//   },
//   numeroInventaire: 'INV-123',
//   numeroSerie: 'SN-456',
//   poleExcellences: [{ id: 'pole1' }],
//   projetFinancements: [{ id: 'proj1' }, { id: 'proj2' }],
//   reparateurs: [{ id: 'rep1' }, { id: 'rep2' }],
//   responsablesSST: [{ id: 'sst1' }],
//   responsablesTechnique: [{ id: 'tech1' }, { id: 'tech2' }],
//   risques: {
//     en: 'Risks in English',
//     fr: 'Risques en français',
//   },
//   techniques: [{ id: 'tech1' }, { id: 'tech2' }],
//   titulaire: { id: 'holder1' },
//   typeEquipement: { id: 'type1' },
//   utilisations: {
//     en: 'Usage contexts in English',
//     fr: 'Contextes d\'utilisation en français',
//   },
// };
