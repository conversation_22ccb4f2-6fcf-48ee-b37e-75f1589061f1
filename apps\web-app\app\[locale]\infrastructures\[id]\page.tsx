import Details from '@/app/[locale]/infrastructures/[id]/details';
import { getQueryClientOptions } from '@/constants/query-client';
import { infrastructureByIdOptions } from '@/hooks/infrastructure/useGetInfrastructureById';
import { mapInfrastructureAndEquipments } from '@/services/mappers/infrastructures';
import type { PageDetailsParams } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';

export default async function InfrastructureDescription(
  props: PageDetailsParams,
) {
  const params = await props.params;

  const { id, locale } = params;

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  await queryClient.prefetchQuery(
    infrastructureByIdOptions(id, locale, {
      select: mapInfrastructureAndEquipments,
    }),
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Details />
    </HydrationBoundary>
  );
}
