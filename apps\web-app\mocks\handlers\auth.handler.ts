import { env } from '@/env';
import { http, HttpResponse } from 'msw';

export const authHandlers = [
  http.get(
    `${env.NEXT_PUBLIC_API_BASE_URL}/v2/auth/get-session`,
    ({ cookies }) => {
      if (cookies.session) {
        return HttpResponse.json({
          session: { id: 'session-id', user: { id: 'user-id' } },
        });
      }
      return new HttpResponse(null, { status: 401 });
    },
  ),
];
