import {
  type RieServiceParams,
  defaultRieServiceParams,
} from '@/constants/rie-client';
import { rieClient } from '@/services/client/client';
import type {
  ApiInfiniteReturnType,
  ApiResponse,
  ApiReturnType,
} from '@/types/common';
import type { EquipmentFull } from '@/types/equipment';
import type { SupportedLocale } from '@/types/locale';

export const getAllEquipments = async (
  params: RieServiceParams,
  queryParams: string,
): Promise<ApiReturnType<EquipmentFull[]>> => {
  const response = await rieClient.get<ApiResponse<EquipmentFull[]>>(
    `explorer/equipement${queryParams.length ? `?${queryParams}` : ''}`,
    {
      params,
    },
  );

  return {
    count: response.data.info.total_records,
    data: response.data.data,
    facets: response.data.facets,
  };
};

export const getEquipmentById = async (
  id: string,
  locale: SupportedLocale,
): Promise<{ equipment: EquipmentFull }> => {
  const response = await rieClient.get<{ data: EquipmentFull }>(
    `explorer/equipement/${id}`,
    {
      params: defaultRieServiceParams(locale),
    },
  );
  return {
    equipment: response.data.data,
  };
};

export const getEquipmentsByInfrastructureId = async (
  id: string,
  locale: SupportedLocale,
): Promise<ApiReturnType<EquipmentFull[]>> => {
  const response = await rieClient.get<ApiResponse<EquipmentFull[]>>(
    `explorer/infrastructure/${id}/equipement`,
    {
      params: defaultRieServiceParams(locale),
    },
  );

  return {
    count: response.data.info.total_records,
    data: response.data.data,
    facets: response.data.facets,
  };
};

export const createEquipment = async (
  payload: FormData,
): Promise<EquipmentFull> => {
  const response = await rieClient.postForm<EquipmentFull>(
    'rest/equipement/?format=json',
    payload,
  );
  return response.data;
};

export const getEquipmentsList = async ({
  pageParam,
  params,
  queryParams,
  viewOwnEquipments,
}: {
  pageParam: number;
  params: RieServiceParams;
  queryParams: string;
  viewOwnEquipments: boolean;
}): Promise<ApiInfiniteReturnType<EquipmentFull[]>> => {
  const response = await rieClient.get<ApiResponse<EquipmentFull[]>>(
    `explorer/equipement?start=${pageParam}${viewOwnEquipments ? '&userdata=1' : ''}${queryParams.length ? `&${queryParams}` : ''}`,
    {
      params,
    },
  );

  return {
    count: response.data.info.total_records,
    data: response.data.data,
    facets: response.data.facets,
    offset: response.data.info.start,
  };
};

export const updateEquipment = async (
  id: string,
  payload: FormData, //Avant on avait: EquipmentPutFormSchema
): Promise<EquipmentFull> => {
  const response = await rieClient.post<ApiResponse<EquipmentFull>>(
    `rest/equipement/${id}/`,
    payload,
  );
  return response.data.data;
};

export const deleteEquipment = async (id: string): Promise<{ id: string }> => {
  const response = await rieClient.delete<{ id: string }>(
    `rest/equipement/${id}/?format=json`,
  );
  return response.data;
};
