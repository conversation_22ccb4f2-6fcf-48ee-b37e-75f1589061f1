import { PgDatabaseLayer } from '@rie/postgres-db';
import { InfrastructuresRepositoryLive } from '@rie/repositories';
import { InfrastructuresServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const InfrastructuresServicesLayer = Layer.mergeAll(
  InfrastructuresRepositoryLive.Default,
  InfrastructuresServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const InfrastructuresRuntime = ManagedRuntime.make(
  InfrastructuresServicesLayer,
);
