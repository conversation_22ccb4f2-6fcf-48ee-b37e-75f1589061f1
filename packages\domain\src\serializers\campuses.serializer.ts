import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  CampusEditSchema,
  CampusListSchema,
  CampusSelectSchema,
} from '../schemas';
import type {
  Campus,
  CampusList,
  CampusSelect,
  CollectionViewType,
  ResourceViewType,
} from '../types';

// Schema transformer for converting database campus to list view
export const DbCampusToCampusList = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.String,
    sadId: Schema.NullishOr(Schema.String),
    institutionId: Schema.NullishOr(Schema.String),
    createdAt: Schema.String,
    updatedAt: Schema.String,
    modifiedBy: Schema.NullishOr(Schema.String),
    translations: Schema.Array(
      Schema.Struct({
        id: Schema.String,
        locale: Schema.String,
        name: Schema.NullishOr(Schema.String),
      }),
    ),
    institution: Schema.NullishOr(
      Schema.Struct({
        id: Schema.String,
        translations: Schema.Array(
          Schema.Struct({
            id: Schema.String,
            locale: Schema.String,
            name: Schema.NullishOr(Schema.String),
          }),
        ),
      }),
    ),
  }),
  CampusListSchema,
  {
    strict: false, // Allow missing fields like institutions serializer
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation (could be 'fr' or 'en')
          const defaultTranslation =
            raw.translations?.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations?.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations?.[0];

          // Get institution name from institution relation
          let institutionName = null;
          if (raw.institution?.translations) {
            const institutionTranslations = raw.institution.translations;
            institutionName =
              institutionTranslations.find((t) => t.locale === 'fr')?.name ||
              institutionTranslations.find((t) => t.locale === 'en')?.name ||
              institutionTranslations?.[0]?.name ||
              null;
          }

          return {
            id: raw.id,
            text: defaultTranslation?.name || raw.id,
            jurisdiction: institutionName,
            lastUpdatedAt: raw.updatedAt || null,
            sadId: raw.sadId || null,
            institutionId: raw.institutionId || null,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse campus for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database campus to select view
export const DbCampusToCampusSelect = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.String,
    sadId: Schema.NullishOr(Schema.String),
    institutionId: Schema.NullishOr(Schema.String),
    createdAt: Schema.String,
    updatedAt: Schema.String,
    modifiedBy: Schema.NullishOr(Schema.String),
    translations: Schema.Array(
      Schema.Struct({
        id: Schema.String,
        locale: Schema.String,
        name: Schema.NullishOr(Schema.String),
      }),
    ),
    institution: Schema.NullishOr(
      Schema.Struct({
        id: Schema.String,
        translations: Schema.Array(
          Schema.Struct({
            id: Schema.String,
            locale: Schema.String,
            name: Schema.NullishOr(Schema.String),
          }),
        ),
      }),
    ),
  }),
  CampusSelectSchema,
  {
    strict: false, // Allow missing fields
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation for label
          const defaultTranslation =
            raw.translations?.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations?.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations?.[0];

          return {
            value: raw.id,
            label: defaultTranslation?.name || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse campus for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database campus to edit format
export const DbCampusToCampusEdit = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.String,
    sadId: Schema.NullishOr(Schema.String),
    institutionId: Schema.NullishOr(Schema.String),
    createdAt: Schema.String,
    updatedAt: Schema.String,
    modifiedBy: Schema.NullishOr(Schema.String),
    translations: Schema.Array(
      Schema.Struct({
        id: Schema.String,
        locale: Schema.String,
        name: Schema.NullishOr(Schema.String),
      }),
    ),
    institution: Schema.NullishOr(
      Schema.Struct({
        id: Schema.String,
        translations: Schema.Array(
          Schema.Struct({
            id: Schema.String,
            locale: Schema.String,
            name: Schema.NullishOr(Schema.String),
          }),
        ),
      }),
    ),
  }),
  CampusEditSchema,
  {
    strict: false, // Allow missing fields
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            sadId: raw.sadId || null,
            institutionId: raw.institutionId || null,
            translations: (raw.translations || []).map((translation) => ({
              locale: translation.locale as 'fr' | 'en',
              name: translation.name || '',
            })),
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse campus for edit format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Helper function to transform an array of Campus to CampusSelect[]
export const dbCampusesToCampusSelect = (
  dbCampuses: Campus[],
): CampusSelect[] => {
  return dbCampuses.map((dbCampus) => {
    const defaultTranslation =
      dbCampus.translations.find(
        (translation) => translation.locale === 'fr',
      ) ||
      dbCampus.translations.find(
        (translation) => translation.locale === 'en',
      ) ||
      dbCampus.translations[0];

    return {
      value: dbCampus.id,
      label: defaultTranslation?.name || dbCampus.id,
    };
  });
};

// Helper function to transform campuses to list (like infrastructures)
export const dbCampusesToList = (
  dbCampuses: Array<{
    id: string;
    sadId: string | null;
    institutionId: string | null;
    createdAt: string;
    updatedAt: string;
    modifiedBy: string | null;
    translations: Array<{
      id: string;
      locale: string;
      name: string | null;
    }>;
    institution?: {
      id: string;
      translations: Array<{
        id: string;
        locale: string;
        name: string | null;
      }>;
    } | null;
  }>,
): CampusList[] => {
  return dbCampuses.map((campus) => {
    try {
      return Schema.decodeUnknownSync(DbCampusToCampusList)(campus);
    } catch (error) {
      console.error('Error transforming campus:', error, campus);
      // Return a fallback object to prevent crashes
      return {
        id: campus.id,
        text: campus.id,
        jurisdiction: null,
        lastUpdatedAt: campus.updatedAt || '',
        sadId: campus.sadId || null,
        institutionId: campus.institutionId || null,
      };
    }
  });
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbCampusesToCampuses = (
  dbCampuses: Array<{
    id: string;
    sadId: string | null;
    institutionId: string | null;
    createdAt: string;
    updatedAt: string;
    modifiedBy: string | null;
    translations: Array<{
      id: string;
      locale: string;
      name: string | null;
    }>;
    institution?: {
      id: string;
      translations: Array<{
        id: string;
        locale: string;
        name: string | null;
      }>;
    } | null;
  }>,
  view: CollectionViewType,
): CampusList[] | CampusSelect[] => {
  if (view === 'select') {
    // Convert to simple value/label pairs like infrastructures
    return dbCampuses.map((campus) => {
      const translations = campus.translations || [];
      const frName = translations.find((t) => t.locale === 'fr')?.name;
      const enName = translations.find((t) => t.locale === 'en')?.name;
      const label = frName || enName || translations?.[0]?.name || campus.id;
      return { value: campus.id, label: String(label ?? campus.id) };
    });
  }
  return dbCampusesToList(dbCampuses);
};

// New serializer function for Campus with view parameter
export const dbCampusToCampus = (
  dbCampus: {
    id: string;
    sadId: string | null;
    institutionId: string | null;
    createdAt: string;
    updatedAt: string;
    modifiedBy: string | null;
    translations: Array<{
      id: string;
      locale: string;
      name: string | null;
    }>;
    institution?: {
      id: string;
      translations: Array<{
        id: string;
        locale: string;
        name: string | null;
      }>;
    } | null;
  },
  view: ResourceViewType,
) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbCampusToCampusEdit)(dbCampus)
    : Schema.decodeUnknownSync(DbCampusToCampusList)(dbCampus);
};
