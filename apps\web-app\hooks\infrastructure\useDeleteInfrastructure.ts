import { useToast } from '@/components/hooks/use-toast';
import { deleteInfrastructure } from '@/services/infrastructures';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';

export const useDeleteInfrastructure = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  const tCommon = useTranslations('common');
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => deleteInfrastructure(id),
    onError: (error) => {
      let errorMessage = tCommon('notifications.errors.onDeleteResource', {
        resource: tCommon('resources.infrastructure'),
      });

      if (error instanceof Error) {
        errorMessage = error.message;
      } else {
        errorMessage = `${tCommon('notifications.errors.generic')}: ${String(error)}`;
      }
      toast({
        description: errorMessage,
        title: tCommon('notifications.errorTitle'),
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        description: tCommon('notifications.success', {
          action: tCommon('actions.deleted'),
          resource: tCommon('resources.infrastructure'),
        }),
        title: tCommon('notifications.successTitle'),
        variant: 'success',
      });

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['allInfrastructures'] });
      queryClient.invalidateQueries({ queryKey: ['infiniteInfrastructures'] });
      queryClient.invalidateQueries({ queryKey: ['infrastructuresList'] });

      if (onSuccess) {
        onSuccess();
      }
    },
  });
};
