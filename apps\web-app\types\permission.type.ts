import type {
  operationsMap,
  resourceMap,
} from '@/constants/functionality.constant';
import type {
  CollectionViewParamType,
  PermissionDetail,
  PermissionGroupEdit,
  PermissionSelect,
  PermissionsGroupDetail,
  PermissionsGroupList,
  PermissionsGroupSelect,
  ResourceViewType,
  RoleList,
  RoleSelect,
} from '@rie/domain/types';

export type OperationKey = keyof typeof operationsMap;
export type Operation = (typeof operationsMap)[OperationKey];

export type ResourceKey = keyof typeof resourceMap;
export type Resource = (typeof resourceMap)[ResourceKey];

export type Permission = { operation: Operation; resource: Resource };

export type PermissionMap = Record<number, Permission>;

export type UserPermissionStatus = 'denied' | 'granted' | 'unknown';

export type CollectionPermissionGroupResultType<
  View extends CollectionViewParamType['view'],
> = View extends 'select' ? PermissionsGroupSelect[] : PermissionsGroupList[];

export type ResourcePermissionGroupResultType<View extends ResourceViewType> =
  View extends 'detail' ? PermissionsGroupDetail : PermissionGroupEdit;

export type PermissionResultType<View extends CollectionViewParamType['view']> =
  View extends 'select' ? PermissionSelect[] : PermissionDetail[];

export type ResourcePermissionResultType<View extends ResourceViewType> =
  View extends 'detail' ? PermissionDetail : PermissionDetail;

export type RoleResultType<View extends CollectionViewParamType['view']> =
  View extends 'select' ? RoleSelect[] : RoleList[];
