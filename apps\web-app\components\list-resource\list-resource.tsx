'use client';

import { BaseTable } from '@/components/base-table/base-table';
import { DataTable } from '@/components/data-table/data-table';
import { TablePagination } from '@/components/table-pagination/table-pagination';

import type {
  ColumnDef,
  ColumnPinningState,
  VisibilityState,
} from '@tanstack/react-table';
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useState,
} from 'react';

type ResourceListProps<T extends object, C extends object> = {
  columns: ColumnDef<T, C>[];
  data: T[];
  initialColumnVisibility: VisibilityState;
  isLoading?: boolean;
  onPageChange: Dispatch<
    SetStateAction<{
      pageIndex: number;
      pageSize: number;
    }>
  >;
  pageIndex: number;
  pageSize: number;
  resourceName: string;
  totalCount: number;
};

export const ResourceList = <T extends object, C extends object>({
  columns,
  data,
  initialColumnVisibility,
  isLoading,
  onPageChange,
  pageIndex,
  pageSize,
  resourceName,
  totalCount,
}: ResourceListProps<T, C>) => {
  const [pinnedColumns, setPinnedColumns] = useState<ColumnPinningState>({
    left: [],
  });

  const onColumnPin = useCallback(() => {
    setPinnedColumns((prev) => ({ ...prev, left: ['name'] }));
  }, []);

  const onColumnUnPin = useCallback(() => {
    setPinnedColumns((prev) => ({ ...prev, left: [] }));
  }, []);

  return (
    <BaseTable
      columns={columns}
      data={data}
      enablePagination
      initialColumnVisibility={initialColumnVisibility}
      pinnedColumns={pinnedColumns}
      pageIndex={pageIndex}
      pageSize={pageSize}
      totalCount={totalCount}
      resourceName={resourceName}
      render={(table) => (
        <div className="mt-[0.563rem] rounded-md bg-white">
          <DataTable
            columns={columns}
            isLoading={Boolean(isLoading)}
            onColumnPinAction={onColumnPin}
            onColumnUnPinAction={onColumnUnPin}
            table={table}
          />
          <TablePagination
            className="mt-1"
            setPagination={onPageChange}
            table={table}
          />
        </div>
      )}
    />
  );
};
