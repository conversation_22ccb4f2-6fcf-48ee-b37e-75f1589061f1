import {
  isInfrastructureOrEquipmentRoute,
  isProtectedRoute,
} from '@/helpers/auth';
import {
  defaultLocale,
  localePrefix,
  locales,
  pathnames,
} from '@/i18n/settings';
import createIntlMiddleware from 'next-intl/middleware';
import { type NextRequest, NextResponse } from 'next/server';

const handleI18nRouting = createIntlMiddleware({
  defaultLocale,
  localePrefix,
  locales,
  pathnames,
});

export default async function middleware(request: NextRequest) {
  const response = handleI18nRouting(request);
  const path = request.nextUrl.pathname.replace(/^\/(fr|en)/, '');

  if (isInfrastructureOrEquipmentRoute(path)) {
    response.headers.set('x-searchParams', request.nextUrl.search.slice(1));
  }

  if (isProtectedRoute(path)) {
    const sessionCookie = request.cookies.get('rie-session')?.value;
    if (!sessionCookie) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('from', path);
      return NextResponse.redirect(loginUrl);
    }
  }

  return response;
}

export const config = {
  // Match only internationalized pathnames
  matcher: ['/((?!api|_next|_vercel|.*\\..*).*)'],
};
