'use client';

import { fundingProjectsColumns } from '@/app/[locale]/bottin/projets-financement/columns';
import { GenericList } from '@/components/bottin-table/generic-list';
import { initialColumnVisibility } from '@/constants/bottin/funding-projects';
import type { SupportedLocale } from '@/types/locale';
import type { FundingProjectList } from '@rie/domain/types';

type FundingProjectsListProps = {
  locale: SupportedLocale;
};

export const FundingProjectsList = ({ locale }: FundingProjectsListProps) => {
  return (
    <GenericList<
      'fundingProjects',
      'list',
      FundingProjectList,
      Record<string, unknown>
    >
      controlledListKey="fundingProjects"
      view="list"
      locale={locale}
      columns={fundingProjectsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="funding-projects"
    />
  );
};
