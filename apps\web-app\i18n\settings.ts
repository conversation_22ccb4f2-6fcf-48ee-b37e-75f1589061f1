import type { CamelCase } from '@/types/common';

export const defaultLocale = 'fr' as const;
export const locales = [defaultLocale, 'en'] as const;

export const port = process.env.PORT || 3000;
export const host = `http://localhost:${port}`;

export const pathnames = {
  '/': {
    en: '/',
    fr: '/',
  },
  '/admin/permissions': {
    en: '/admin/permissions',
    fr: '/admin/permissions',
  },
  '/admin/permissions/[id]/editer': {
    en: '/admin/permissions/[id]/edit',
    fr: '/admin/permissions/[id]/editer',
  },
  '/admin/permissions/ajouter': {
    en: '/admin/permissions/add',
    fr: '/admin/permissions/ajouter',
  },
  '/admin/groupes-permissions': {
    en: '/admin/permissions-groups',
    fr: '/admin/groupes-permissions',
  },
  '/admin/groupes-permissions/[id]/editer': {
    en: '/admin/permissions-groups/[id]/edit',
    fr: '/admin/groupes-permissions/[id]/editer',
  },
  '/admin/groupes-permissions/ajouter': {
    en: '/admin/permissions-groups/add',
    fr: '/admin/groupes-permissions/ajouter',
  },
  '/admin/roles': {
    en: '/admin/roles',
    fr: '/admin/roles',
  },
  '/admin/roles/[id]/editer': {
    en: '/admin/roles/[id]/edit',
    fr: '/admin/roles/[id]/editer',
  },
  '/admin/roles/ajouter': {
    en: '/admin/roles/add',
    fr: '/admin/roles/ajouter',
  },
  '/admin/roles-utilisateurs': {
    en: '/admin/user-roles',
    fr: '/admin/roles-utilisateurs',
  },
  '/administration': {
    en: '/administration',
    fr: '/administration',
  },
  '/bottin/batiments': {
    en: '/directory/buildings',
    fr: '/bottin/batiments',
  },
  '/bottin/batiments/[id]': {
    en: '/directory/buildings/[id]',
    fr: '/bottin/batiments/[id]',
  },
  '/bottin/batiments/[id]/editer': {
    en: '/directory/buildings/[id]/edit',
    fr: '/bottin/batiments/[id]/editer',
  },
  '/bottin/batiments/ajouter': {
    en: '/directory/buildings/add',
    fr: '/bottin/batiments/ajouter',
  },
  '/bottin/campus': {
    en: '/directory/campus',
    fr: '/bottin/campus',
  },
  '/bottin/campus/[id]': {
    en: '/directory/campus/[id]',
    fr: '/bottin/campus/[id]',
  },
  '/bottin/campus/[id]/editer': {
    en: '/directory/campus/[id]/edit',
    fr: '/bottin/campus/[id]/editer',
  },
  '/bottin/campus/ajouter': {
    en: '/directory/campus/add',
    fr: '/bottin/campus/ajouter',
  },
  '/bottin/etablissements': {
    en: '/directory/establishments',
    fr: '/bottin/etablissements',
  },
  '/bottin/etablissements/[id]': {
    en: '/directory/establishments/[id]',
    fr: '/bottin/etablissements/[id]',
  },
  '/bottin/etablissements/[id]/editer': {
    en: '/directory/establishments/[id]/edit',
    fr: '/bottin/etablissements/[id]/editer',
  },
  '/bottin/etablissements/ajouter': {
    en: '/directory/establishments/add',
    fr: '/bottin/etablissements/ajouter',
  },
  '/bottin/locaux': {
    en: '/directory/rooms',
    fr: '/bottin/locaux',
  },
  '/bottin/locaux/[id]': {
    en: '/directory/rooms/[id]',
    fr: '/bottin/locaux/[id]',
  },
  '/bottin/locaux/[id]/editer': {
    en: '/directory/rooms/[id]/edit',
    fr: '/bottin/locaux/[id]/editer',
  },
  '/bottin/locaux/ajouter': {
    en: '/directory/rooms/add',
    fr: '/bottin/locaux/ajouter',
  },
  '/bottin/manufacturiers': {
    en: '/directory/manufacturers',
    fr: '/bottin/manufacturiers',
  },
  '/bottin/manufacturiers/[id]': {
    en: '/directory/manufacturers/[id]',
    fr: '/bottin/manufacturiers/[id]',
  },
  '/bottin/manufacturiers/[id]/editer': {
    en: '/directory/manufacturers/[id]/edit',
    fr: '/bottin/manufacturiers/[id]/editer',
  },
  '/bottin/manufacturiers/ajouter': {
    en: '/directory/manufacturers/add',
    fr: '/bottin/manufacturiers/ajouter',
  },
  '/bottin/personnes': {
    en: '/directory/people',
    fr: '/bottin/personnes',
  },
  '/bottin/personnes/[id]': {
    en: '/directory/people/[id]',
    fr: '/bottin/personnes/[id]',
  },
  '/bottin/personnes/[id]/editer': {
    en: '/directory/people/[id]/edit',
    fr: '/bottin/personnes/[id]/editer',
  },
  '/bottin/personnes/ajouter': {
    en: '/directory/people/add',
    fr: '/bottin/personnes/ajouter',
  },
  '/bottin/projets-financement': {
    en: '/directory/financing-projects',
    fr: '/bottin/projets-financement',
  },
  '/bottin/projets-financement/[id]': {
    en: '/directory/financing-projects/[id]',
    fr: '/bottin/projets-financement/[id]',
  },
  '/bottin/projets-financement/[id]/editer': {
    en: '/directory/financing-projects/[id]/edit',
    fr: '/bottin/projets-financement/[id]/editer',
  },
  '/bottin/projets-financement/ajouter': {
    en: '/directory/financing-projects/add',
    fr: '/bottin/projets-financement/ajouter',
  },
  '/bottin/unites': {
    en: '/directory/units',
    fr: '/bottin/unites',
  },
  '/bottin/unites/[id]': {
    en: '/directory/units/[id]',
    fr: '/bottin/unites/[id]',
  },
  '/bottin/unites/[id]/editer': {
    en: '/directory/units/[id]/edit',
    fr: '/bottin/unites/[id]/editer',
  },
  '/bottin/unites/ajouter': {
    en: '/directory/units/add',
    fr: '/bottin/unites/ajouter',
  },
  '/contrats-service': {
    en: '/service-contracts',
    fr: '/contrats-service',
  },
  '/contrats-service/[id]': {
    en: '/service-contracts/[id]',
    fr: '/contrats-service/[id]',
  },
  '/equipements': {
    en: '/equipments',
    fr: '/equipements',
  },
  '/equipements/[id]': {
    en: '/equipments/[id]',
    fr: '/equipements/[id]',
  },
  '/equipements/[id]/editer': {
    en: '/equipments/[id]/edit',
    fr: '/equipements/[id]/editer',
  },
  '/equipements/ajouter': {
    en: '/equipments/add',
    fr: '/equipements/ajouter',
  },
  '/infrastructures': {
    en: '/infrastructures',
    fr: '/infrastructures',
  },
  '/infrastructures/[id]': {
    en: '/infrastructures/[id]',
    fr: '/infrastructures/[id]',
  },
  '/infrastructures/[id]/editer': {
    en: '/infrastructures/[id]/edit',
    fr: '/infrastructures/[id]/editer',
  },
  '/infrastructures/ajouter': {
    en: '/infrastructures/add',
    fr: '/infrastructures/ajouter',
  },
  '/infrastructures/ajouter#affiliations': {
    en: '/infrastructures/add#affiliations',
    fr: '/infrastructures/ajouter#affiliations',
  },
  '/infrastructures/ajouter#description': {
    en: '/infrastructures/add#descritpion',
    fr: '/infrastructures/ajouter#description',
  },
  '/infrastructures/ajouter#permissions': {
    en: '/infrastructures/add#permissions',
    fr: '/infrastructures/ajouter#permissions',
  },

  '/listes-controlees/deux': {
    en: '/controlled-lists/two',
    fr: '/listes-controlees/deux',
  },
  '/listes-controlees/un': {
    en: '/controlled-lists/one',
    fr: '/listes-controlees/un',
  },
  '/login': {
    en: '/login',
    fr: '/login',
  },
  '/profil': {
    en: '/profile',
    fr: '/profil',
  },
} as const;

// Use the default: `always`
export const localePrefix = 'always' as const;

export type AppPathnames = keyof typeof pathnames;
export type SideMenuPathnames = Exclude<AppPathnames, `${string}[id]${string}`>;

export type EquipmentInfrastructureEditPathnames = Extract<
  AppPathnames,
  `/equipements/[id]/editer` | `/infrastructures/[id]/editer`
>;

export type AppPathnamesWithId = Extract<
  AppPathnames,
  `${string}[id]${string}`
>;

export type DirectoryAddPathnames = Extract<
  SideMenuPathnames,
  `/bottin/${string}/ajouter`
>;

export type DirectoryEditPathnames = Extract<
  AppPathnames,
  `/bottin/${string}/[id]/editer`
>;

export type AdminEditPathnames = Extract<
  AppPathnames,
  `/admin/${string}/[id]/editer`
>;

export type AppEditPathnames =
  | EquipmentInfrastructureEditPathnames
  | DirectoryEditPathnames
  | AdminEditPathnames;

export type DirectoryEntityKey = {
  [K in DirectoryAddPathnames]: (typeof pathnames)[K]['en'] extends `/directory/${infer Entity}/add`
    ? CamelCase<Entity>
    : never;
}[DirectoryAddPathnames];
