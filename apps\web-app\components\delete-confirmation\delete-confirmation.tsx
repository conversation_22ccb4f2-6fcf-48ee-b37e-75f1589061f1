import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/ui/alert-dialog';
import { Button } from '@/ui/button';
import { useTranslations } from 'next-intl';
import type { ReactElement } from 'react';

type DeleteConfirmationProps = {
  onDeleteAction: () => void;
  title: string;
  trigger: ReactElement;
};

export function DeleteConfirmation({
  onDeleteAction,
  title,
  trigger,
}: DeleteConfirmationProps) {
  const tCommon = useTranslations('common');
  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>{trigger}</AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>
            {tCommon('permanentDeleteAction')}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>{tCommon('cancel')}</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              className="flex bg-destructive hover:bg-destructive/90"
              onClick={onDeleteAction}
              variant="destructive"
            >
              {tCommon('delete')}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
