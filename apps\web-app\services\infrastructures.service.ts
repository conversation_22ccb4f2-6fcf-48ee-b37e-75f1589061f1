import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type {
  CollectionViewParamType,
  InfrastructureList,
  ResourceViewType,
} from '@rie/domain/types';

export const getInfrastructuresCount = async (): Promise<number> => {
  try {
    const client = await getApiClient();
    const data = await client
      .get('v2/infrastructures', { searchParams: { limit: 0 } })
      .json<{ count: number }>();
    return data.count;
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch infrastructures count');
  }
};

export const getAllInfrastructures = async <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) => {
  try {
    const client = await getApiClient();
    return await client
      .get<InfrastructureList[]>('v2/infrastructures', {
        searchParams: { view },
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch infrastructures');
  }
};

type GetInfrastructureByIdParams = { id: string; view: ResourceViewType };

export const getInfrastructureById = async ({
  id,
  view,
}: GetInfrastructureByIdParams) => {
  try {
    const client = await getApiClient();
    return await client
      .get<InfrastructureList>(`v2/infrastructures/${id}`, {
        searchParams: { view },
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch infrastructure');
  }
};
