import { effectValidator } from '@hono/effect-validator';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

import { handleEffectError } from '@/api/v2/utils/error-handler';
import { VendorsRuntime } from '@/infrastructure/runtimes/vendors.runtime';
import * as VendorsSchemas from '@rie/domain/schemas';
import {
  CollectionViewParamSchema,
  VendorListSchema,
  VendorSchema,
} from '@rie/domain/schemas';
// no-op types removed
import { dbVendorsToVendors } from '@rie/domain/serializers';
import type { HonoVariables } from '@rie/domain/types';
import { VendorsServiceLive } from '@rie/services';

export const createVendorRoute = describeRoute({
  description: 'Créer un manufacturier',
  operationId: 'createVendor',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(
          Schema.Struct({
            id: Schema.optional(Schema.String),
            name: Schema.Array(
              Schema.Struct({ locale: Schema.String, value: Schema.String }),
            ),
            alias: Schema.Array(
              Schema.Struct({ locale: Schema.String, value: Schema.String }),
            ),
            dateEnd: Schema.NullishOr(Schema.String),
            contacts: Schema.optional(Schema.Array(Schema.Unknown)),
            phones: Schema.optional(
              Schema.Array(
                Schema.Struct({
                  description: Schema.Array(
                    Schema.Struct({
                      locale: Schema.String,
                      value: Schema.String,
                    }),
                  ),
                  phone: Schema.NullishOr(Schema.String),
                }),
              ),
            ),
          }),
        ),
        example: {
          name: [
            { locale: 'en', value: 'Acme Corporation' },
            { locale: 'fr', value: 'Corporation Acme' },
          ],
          alias: [
            { locale: 'en', value: 'ACME' },
            { locale: 'fr', value: 'ACME' },
          ],
          dateEnd: null,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(VendorSchema),
        },
      },
      description: 'Manufacturier créé avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier déjà existant',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const updateVendorRoute = describeRoute({
  description: 'Mettre à jour un manufacturier',
  operationId: 'updateVendor',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(VendorsSchemas.ResourceIdSchema),
      description: 'ID du manufacturier à mettre à jour',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(
          Schema.Struct({
            id: Schema.optional(Schema.String),
            name: Schema.Array(
              Schema.Struct({ locale: Schema.String, value: Schema.String }),
            ),
            alias: Schema.Array(
              Schema.Struct({ locale: Schema.String, value: Schema.String }),
            ),
            dateEnd: Schema.NullishOr(Schema.String),
            contacts: Schema.optional(Schema.Array(Schema.Unknown)),
            phones: Schema.optional(
              Schema.Array(
                Schema.Struct({
                  description: Schema.Array(
                    Schema.Struct({
                      locale: Schema.String,
                      value: Schema.String,
                    }),
                  ),
                  phone: Schema.NullishOr(Schema.String),
                }),
              ),
            ),
          }),
        ),
        example: {
          name: [
            { locale: 'en', value: 'Updated Acme Corporation' },
            { locale: 'fr', value: 'Corporation Acme Mise à Jour' },
          ],
          alias: [],
          dateEnd: '2024-12-31',
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(VendorSchema),
        },
      },
      description: 'Manufacturier mis à jour avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const deleteVendorRoute = describeRoute({
  description: 'Supprimer un manufacturier',
  operationId: 'deleteVendor',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(VendorsSchemas.ResourceIdSchema),
      description: 'ID du manufacturier à supprimer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              success: Schema.Boolean,
              message: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier supprimé avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const getAllVendorsRoute = describeRoute({
  description: 'Lister tous les manufacturiers',
  operationId: 'getAllVendors',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(CollectionViewParamSchema),
      description: 'Vue des données à retourner (list ou select)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(VendorListSchema)),
        },
      },
      description:
        'Manufacturiers retournés avec succès. Le format dépend du paramètre "view": "list" retourne tous les champs (id, text, dateEnd, lastUpdatedAt), "select" retourne le format value/label pour les dropdowns.',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturiers non trouvés',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

export const getVendorByIdRoute = describeRoute({
  description: 'Obtenir un manufacturier par ID',
  operationId: 'getVendorById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(VendorsSchemas.ResourceIdSchema),
      description: 'ID du manufacturier à récupérer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(VendorSchema),
        },
      },
      description: 'Manufacturier retourné avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Vendors'],
});

const vendorsRoute = new Hono<{
  Variables: HonoVariables;
}>();

vendorsRoute.get(
  '/',
  getAllVendorsRoute,
  effectValidator('query', VendorsSchemas.CollectionViewParamSchema),
  async (ctx) => {
    const { view = 'list' } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      const vendors = yield* vendorService.getAllVendors();
      return dbVendorsToVendors(vendors, view);
    });
    const result = await VendorsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.post(
  '/',
  createVendorRoute,
  effectValidator(
    'json',
    Schema.Struct({
      id: Schema.optional(Schema.String),
      name: Schema.Array(
        Schema.Struct({ locale: Schema.String, value: Schema.String }),
      ),
      alias: Schema.Array(
        Schema.Struct({ locale: Schema.String, value: Schema.String }),
      ),
      dateEnd: Schema.NullishOr(Schema.String),
      contacts: Schema.optional(Schema.Array(Schema.Unknown)),
      phones: Schema.optional(
        Schema.Array(
          Schema.Struct({
            description: Schema.Array(
              Schema.Struct({ locale: Schema.String, value: Schema.String }),
            ),
            phone: Schema.NullishOr(Schema.String),
          }),
        ),
      ),
    }),
  ),
  async (ctx) => {
    type VendorFormBody = {
      id?: string;
      name: { locale: string; value: string }[];
      alias: { locale: string; value: string }[];
      dateEnd?: string | null;
      contacts?: unknown[];
      phones?: {
        description: { locale: string; value: string }[];
        phone?: string | null;
      }[];
    };
    const body = ctx.req.valid('json') as VendorFormBody;
    const user = ctx.get('user');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      const translations = body.name.map(({ locale, value }) => {
        const aliasItem = body.alias.find((a) => a.locale === locale);
        return {
          locale: locale as 'fr' | 'en',
          name: value,
          website: undefined,
          description: undefined,
          otherNames: aliasItem?.value,
        };
      });
      const vendor = {
        startDate: null,
        endDate: body.dateEnd ?? null,
        translations,
        modifiedBy: user?.id,
      };
      return yield* vendorService.createVendor(vendor);
    });

    const result = await VendorsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.put(
  '/:id',
  updateVendorRoute,
  effectValidator(
    'param',
    Schema.Struct({ id: VendorsSchemas.ResourceIdSchema }),
  ),
  effectValidator(
    'json',
    Schema.Struct({
      id: Schema.optional(Schema.String),
      name: Schema.Array(
        Schema.Struct({ locale: Schema.String, value: Schema.String }),
      ),
      alias: Schema.Array(
        Schema.Struct({ locale: Schema.String, value: Schema.String }),
      ),
      dateEnd: Schema.NullishOr(Schema.String),
      contacts: Schema.optional(Schema.Array(Schema.Unknown)),
      phones: Schema.optional(
        Schema.Array(
          Schema.Struct({
            description: Schema.Array(
              Schema.Struct({ locale: Schema.String, value: Schema.String }),
            ),
            phone: Schema.NullishOr(Schema.String),
          }),
        ),
      ),
    }),
  ),
  async (ctx) => {
    type VendorFormBody = {
      id?: string;
      name: { locale: string; value: string }[];
      alias: { locale: string; value: string }[];
      dateEnd?: string | null;
      contacts?: unknown[];
      phones?: {
        description: { locale: string; value: string }[];
        phone?: string | null;
      }[];
    };
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json') as VendorFormBody;
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      const translations = body.name.map(({ locale, value }) => {
        const aliasItem = body.alias.find((a) => a.locale === locale);
        return {
          locale: locale as 'fr' | 'en',
          name: value,
          website: undefined,
          description: undefined,
          otherNames: aliasItem?.value,
        };
      });
      const vendor = {
        startDate: null,
        endDate: body.dateEnd ?? null,
        translations,
        modifiedBy: user?.id,
      };
      return yield* vendorService.updateVendor({ id, vendor });
    });

    const result = await VendorsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.delete(
  '/:id',
  deleteVendorRoute,
  effectValidator(
    'param',
    Schema.Struct({ id: VendorsSchemas.ResourceIdSchema }),
  ),
  async (ctx) => {
    const id = ctx.req.param('id');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      return yield* vendorService.deleteVendor(id);
    });

    const result = await VendorsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({
        success: true,
        message: 'Vendor deleted successfully',
      });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.get(
  '/:id',
  getVendorByIdRoute,
  effectValidator(
    'param',
    Schema.Struct({ id: VendorsSchemas.ResourceIdSchema }),
  ),
  async (ctx) => {
    const id = ctx.req.param('id');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      return yield* vendorService.getVendorById(id);
    });

    const result = await VendorsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { vendorsRoute };
