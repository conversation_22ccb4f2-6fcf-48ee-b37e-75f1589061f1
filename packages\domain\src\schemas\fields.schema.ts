import type {
  OptionalTranslationArgs,
  RequiredTranslationArgs,
} from '../types';
import { createTranslatableFieldMaxLengthSchema } from './base.schema';

/**
 * Creates a name translation schema with a max length of 150 characters
 * and at least one translation is required
 * @param maxLengthErrorMessage
 * @param missingTranslationMessage
 */

export const createRequiredTranslation150Schema = ({
  maxLengthErrorMessage,
  missingTranslationMessage,
}: RequiredTranslationArgs) =>
  createTranslatableFieldMaxLengthSchema({
    field: { maxLengthErrorMessage, maxLength: 150 },
    required: true,
    missingTranslationMessage,
  });

/**
 * Creates an optional description translation schema with a max length of 1500 characters
 * @param maxLengthErrorMessage
 */

export const createOptionalTranslationSchema = ({
  maxLengthErrorMessage,
}: OptionalTranslationArgs) =>
  createTranslatableFieldMaxLengthSchema({
    field: { maxLengthErrorMessage, maxLength: 1500 },
    required: false,
  });
