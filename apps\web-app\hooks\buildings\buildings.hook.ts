import { useToast } from '@/components/hooks/use-toast';
import {
  getAllBuildingsOptions,
  getBuildingByIdOptions,
} from '@/hooks/buildings/buildings.options';
import { useRouter } from '@/lib/navigation';
import {
  createBuilding,
  deleteBuilding,
  updateBuilding,
} from '@/services/buildings/buildings.service';
import type {
  BuildingFormSchemaType,
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const useGetAllBuildings = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) => {
  return useQuery(getAllBuildingsOptions<View>({ view }));
};

interface GetBuildingByIdParams {
  id: string;
  view: ResourceViewType;
}

export const useGetBuildingById = <View extends ResourceViewType>({
  id,
  view,
}: GetBuildingByIdParams) => {
  return useQuery(getBuildingByIdOptions<View>({ id, view }));
};

export const useCreateBuilding = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (payload: BuildingFormSchemaType) => {
      return await createBuilding(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['buildings'] });
      toast({
        title: 'Success',
        description: 'Building created successfully',
        variant: 'success',
      });
      router.push('/bottin/batiments');
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to create building',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateBuilding = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({
      payload,
      id,
    }: { payload: BuildingFormSchemaType; id: string }) =>
      updateBuilding({ id, payload }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['buildings'] });
      toast({
        title: 'Success',
        description: 'Building updated successfully',
        variant: 'success',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to update building',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteBuilding = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => deleteBuilding(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['buildings'] });
      toast({
        title: 'Success',
        description: 'Building deleted successfully',
        variant: 'success',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to delete building',
        variant: 'destructive',
      });
    },
  });
};
