import { handleEffectError } from '@/api/v2/utils/error-handler';
import { InstitutionsRuntime } from '@/infrastructure/runtimes/institutions.runtime';
import { effectValidator } from '@hono/effect-validator';
import {
  CollectionViewParamSchema,
  CollectionViewSchema,
  InstitutionInputSchema,
  InstitutionListSchema,
  InstitutionSchema,
  ResourceIdSchema,
} from '@rie/domain/schemas';
import { dbInstitutionsToInstitutions } from '@rie/domain/serializers';
import type { HonoVariables } from '@rie/domain/types';
import { InstitutionsServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllInstitutionsRoute = describeRoute({
  description: 'Lister toutes les institutions',
  operationId: 'getAllInstitutions',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(CollectionViewSchema),
      description: 'Type de vue (list, select)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(InstitutionListSchema)),
        },
      },
      description: 'Institutions retournées',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

export const getInstitutionByIdRoute = describeRoute({
  description: 'Obtenir une institution par ID',
  operationId: 'getInstitutionById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'institution (format CUID)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(InstitutionSchema),
        },
      },
      description: 'Institution trouvée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Institution non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

export const createInstitutionRoute = describeRoute({
  description: 'Créer une institution',
  operationId: 'createInstitution',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(InstitutionInputSchema),
        example: {
          typeId: 'bqmr7x60br0e6br1rw81uz4j',
          guidId: 'NEW_INSTITUTION_001',
          translations: [
            {
              locale: 'en',
              name: 'New University of Technology',
              description: 'A leading institution in technology and innovation',
            },
            {
              locale: 'fr',
              name: 'Nouvelle Université de Technologie',
              description:
                'Une institution leader en technologie et innovation',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(InstitutionSchema),
        },
      },
      description: 'Institution créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Clé étrangère non trouvée - Le type n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

export const updateInstitutionRoute = describeRoute({
  description: 'Mettre à jour une institution',
  operationId: 'updateInstitution',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'institution (format CUID)",
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(InstitutionInputSchema),
        example: {
          typeId: 'bqmr7x60br0e6br1rw81uz4j',
          guidId: 'UPDATED_INSTITUTION_001',
          translations: [
            {
              locale: 'en',
              name: 'Updated University of Technology',
              description:
                'An updated leading institution in technology and innovation',
            },
            {
              locale: 'fr',
              name: 'Université de Technologie Mise à Jour',
              description:
                'Une institution leader mise à jour en technologie et innovation',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(InstitutionSchema),
        },
      },
      description: 'Institution mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Institution non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

export const deleteInstitutionRoute = describeRoute({
  description: 'Supprimer une institution',
  operationId: 'deleteInstitution',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'institution (format CUID)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Institution supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Institution non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Institutions'],
});

const institutionsRoute = new Hono<{
  Variables: HonoVariables;
}>();

institutionsRoute.get(
  '/',
  getAllInstitutionsRoute,
  effectValidator('query', CollectionViewParamSchema),
  async (ctx) => {
    const { view = 'list' } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      const institutions = yield* institutionService.getAllInstitutions();
      return dbInstitutionsToInstitutions(institutions, view);
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

institutionsRoute.get(
  '/:id',
  getInstitutionByIdRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      return yield* institutionService.getInstitutionById(id);
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

institutionsRoute.post(
  '/',
  createInstitutionRoute,
  effectValidator('json', InstitutionInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      return yield* institutionService.createInstitution({
        ...body,
        modifiedBy: user?.id,
      });
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

institutionsRoute.put(
  '/:id',
  updateInstitutionRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('json', InstitutionInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      return yield* institutionService.updateInstitution({
        id,
        institution: {
          ...body,
          modifiedBy: user?.id,
        },
      });
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

institutionsRoute.delete(
  '/:id',
  deleteInstitutionRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const institutionService = yield* InstitutionsServiceLive;
      return yield* institutionService.deleteInstitution(id);
    });
    const result = await InstitutionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Institution deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { institutionsRoute };
