import { DbRoleSelectSchema } from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import {
  createRequiredStringOfMaxLengthSchema,
  descriptionSchema,
} from './base.schema';

// Schema for permission in role context
const RolePermissionSchema = Schema.Struct({
  id: Schema.String,
  domain: Schema.String,
  action: Schema.String,
});

// Schema for permission group in role context
const RolePermissionGroupSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  description: Schema.NullOr(Schema.String),
});

// Schema for parent role in role context
const ParentRoleSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
});

// Extended schema for role list view with related data
export const RoleListSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  description: Schema.NullOr(Schema.String),
  createdAt: Schema.String,
  updatedAt: Schema.String,
  directPermissions: Schema.Array(RolePermissionSchema),
  permissionsGroups: Schema.Array(RolePermissionGroupSchema),
  parentRoles: Schema.Array(ParentRoleSchema),
});

// Basic role schema for simple operations
export const RoleBasicSchema = DbRoleSelectSchema;

export const RoleSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

const nameSchema = createRequiredStringOfMaxLengthSchema({
  fieldMaxLength: 50,
  errorMessages: {
    required: () => 'Name is required',
    maxLength: (issue) =>
      `Name must be ${issue._tag.length} characters or less`,
  },
});

const permissionGroupIdsSchema = Schema.Array(Schema.String).pipe(
  Schema.minItems(1, {
    message: () => 'At least one permission group is required',
  }),
);
/**
 * Schema for creating a new role
 */
export const RoleInputSchema = Schema.Struct({
  name: nameSchema,
  description: descriptionSchema,
  directPermissionIds: Schema.Array(Schema.String),
  parentRoleIds: Schema.Array(Schema.String),
  permissionGroupIds: permissionGroupIdsSchema,
});

/**
 * Schema for role permissions
 */
export const RolePermissionsSchema = Schema.Struct({
  permissionIds: Schema.Array(Schema.String),
});

/**
 * Schema for role permission groups
 */
export const RolePermissionGroupsSchema = Schema.Struct({
  groupIds: Schema.Array(Schema.String),
});
