import { getAllRoles, getRoleById } from '@/services/permissions/roles.service';
import type { RoleResultType } from '@/types/permission.type';
import type { CollectionViewParamType } from '@rie/domain/types';
import { queryOptions } from '@tanstack/react-query';

export const getAllRolesOptions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) => {
  return queryOptions<RoleResultType<View>>({
    queryFn: () => getAllRoles({ view }) as Promise<RoleResultType<View>>,
    queryKey: ['roles', { view }],
  });
};

export const getRoleByIdOptions = (id: string) => {
  return queryOptions({
    queryFn: () => getRoleById(id),
    queryKey: ['roles', id],
    enabled: !!id,
  });
};
