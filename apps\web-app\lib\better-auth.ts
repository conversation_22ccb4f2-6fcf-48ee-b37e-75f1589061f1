import { redirect } from '@/lib/navigation';
import type { auth } from '@rie/auth';
import { customSessionClient } from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/react';
import { getLocale } from 'next-intl/server';

export const authClient = createAuthClient({
  baseURL: `${process.env.NEXT_PUBLIC_API_BASE_URL}/v2/auth`,
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json',
  },
  fetchOptions: {
    onError: async (context) => {
      const { response } = context;
      if (response.status === 401) {
        // Handle 401 globally
        const locale = await getLocale();
        redirect({ href: { pathname: '/login' }, locale });
      }
    },
  },
  plugins: [customSessionClient<typeof auth>()],
});

export const { signIn, signOut, useSession } = authClient;

export type SessionData = NonNullable<ReturnType<typeof useSession>['data']>;
