import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  FundingProjectEditSchema,
  FundingProjectListSchema,
  FundingProjectSchema,
  FundingProjectSelectSchema,
} from '../schemas';
import type {
  CollectionViewType,
  FundingProject,
  FundingProjectList,
  FundingProjectSelect,
  ResourceViewType,
} from '../types';

// Schema transformer for converting database funding project to list view
export const DbFundingProjectToFundingProjectList = Schema.transformOrFail(
  FundingProjectSchema,
  FundingProjectListSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation (could be 'fr' or 'en')
          const defaultTranslation =
            raw.translations.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations[0];

          return {
            id: raw.id,
            text: defaultTranslation?.name || raw.id,
            titulaire: null, // TODO: Get from holder relation
            infrastructure: null, // TODO: Get from infrastructure relation
            lastUpdatedAt: raw.updatedAt,
            holderId: raw.holderId,
            typeId: raw.typeId,
            fciId: raw.fciId,
            synchroId: raw.synchroId,
            obtainingYear: raw.obtainingYear,
            endDate: raw.endDate,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse funding project for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database funding project to select view
export const DbFundingProjectToFundingProjectSelect = Schema.transformOrFail(
  FundingProjectSchema,
  FundingProjectSelectSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation for label
          const defaultTranslation =
            raw.translations.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations[0];

          return {
            value: raw.id,
            label: defaultTranslation?.name || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse funding project for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database funding project to edit format
export const DbFundingProjectToFundingProjectEdit = Schema.transformOrFail(
  FundingProjectSchema,
  FundingProjectEditSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            holderId: raw.holderId,
            typeId: raw.typeId,
            fciId: raw.fciId,
            synchroId: raw.synchroId,
            obtainingYear: raw.obtainingYear,
            endDate: raw.endDate,
            translations: raw.translations.map((translation) => ({
              locale: translation.locale as 'fr' | 'en',
              name: translation.name || '',
              description: translation.description || undefined,
            })),
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse funding project for edit format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Helper function to transform an array of FundingProject to FundingProjectSelect[]
export const dbFundingProjectsToFundingProjectSelect = (
  dbFundingProjects: FundingProject[],
): FundingProjectSelect[] => {
  return dbFundingProjects.map((dbFundingProject) => {
    const defaultTranslation =
      dbFundingProject.translations.find(
        (translation) => translation.locale === 'fr',
      ) ||
      dbFundingProject.translations.find(
        (translation) => translation.locale === 'en',
      ) ||
      dbFundingProject.translations[0];

    return {
      value: dbFundingProject.id,
      label: defaultTranslation?.name || dbFundingProject.id,
    };
  });
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbFundingProjectsToFundingProjects = (
  dbFundingProjects: FundingProject[],
  view: CollectionViewType,
): FundingProjectList[] | FundingProjectSelect[] => {
  return view === 'select'
    ? dbFundingProjects.map((fundingProject) =>
        Schema.decodeUnknownSync(DbFundingProjectToFundingProjectSelect)(
          fundingProject,
        ),
      )
    : dbFundingProjects.map((fundingProject) =>
        Schema.decodeUnknownSync(DbFundingProjectToFundingProjectList)(
          fundingProject,
        ),
      );
};

// New serializer function for FundingProject with view parameter
export const dbFundingProjectToFundingProject = (
  dbFundingProject: FundingProject,
  view: ResourceViewType,
) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbFundingProjectToFundingProjectEdit)(
        dbFundingProject,
      )
    : Schema.decodeUnknownSync(DbFundingProjectToFundingProjectList)(
        dbFundingProject,
      );
};
