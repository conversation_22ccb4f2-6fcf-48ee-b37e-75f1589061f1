import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { CharacterCount } from '@/components/character-count/character-count';
import { FieldInfo } from '@/components/FieldInfo';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { Textarea } from '@/components/ui/textarea';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { ManufacturerFormSchema } from '@/schemas/bottin/manufacturer-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Input } from '@/ui/input';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

type PhoneFieldProps = {
  index: number;
};

export const PhoneField = ({ index }: PhoneFieldProps) => {
  const t = useTranslations(
    'directory.form.sections.description.contactDetails',
  );
  const tManufacturers = useTranslations(
    'directory.form.sections.description.generalInfo',
  );
  const tCommon = useTranslations('common');
  const tManufacturerBase = useTranslations('directory.form.sections');

  const { control, formState } = useFormContext<ManufacturerFormSchema>();
  const descriptionError = getFieldErrorMessage(
    formState.errors,
    `phones.${index}.description`,
  );
  const descriptionErrorMessage = descriptionError
    ? tManufacturerBase(descriptionError)
    : undefined;
  const descriptionFields = useTranslatedField(
    control,
    `phones.${index}.description`,
  );

  return (
    <>
      <FieldWithTranslations
        control={control}
        errorMessage={descriptionErrorMessage}
        fieldComponent={(field) => <Textarea {...field} />}
        fieldName={`phones.${index}.description`}
        fields={descriptionFields.fields}
        label={(locale) =>
          tManufacturers('fields.description.label', {
            locale: tCommon(locale),
          })
        }
        maxLength={2000}
        onAddTranslation={descriptionFields.handleAddTranslation}
        onRemoveTranslation={descriptionFields.handleRemoveTranslation}
      />
      <FormField
        control={control}
        name={`phones.${index}.phone`}
        render={({ field }) => (
          <FormItem>
            <LabelTooltip
              htmlFor={field.name}
              label={t('fields.phone.label')}
            />
            <FormControl>
              <Input {...field} id={field.name} />
            </FormControl>
            <FieldInfo>
              <FormMessage />
              <CharacterCount count={field.value?.length ?? 0} max={15} />
            </FieldInfo>
          </FormItem>
        )}
      />
    </>
  );
};
