import {
  getGenericByIdOptions,
  getGenericListOptions,
} from '@/hooks/bottin/generic-list.options';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllUnitsOptions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) =>
  getGenericListOptions({ controlledListKey: 'unit', view } as {
    controlledListKey: 'unit';
    view: View;
  });

export const getUnitByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) => getGenericByIdOptions({ controlledListKey: 'unit', id, view } as const);
