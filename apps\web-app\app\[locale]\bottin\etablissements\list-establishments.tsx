'use client';

import { establishmentsColumns } from '@/app/[locale]/bottin/etablissements/columns';
import { GenericList } from '@/components/bottin-table/generic-list';
import { initialColumnVisibility } from '@/constants/bottin/establishments';
import type { SupportedLocale } from '@/types/locale';
import type { InstitutionList } from '@rie/domain/types';

type EstablishmentsListProps = {
  locale: SupportedLocale;
};

export const EstablishmentsList = ({ locale }: EstablishmentsListProps) => {
  return (
    <GenericList<
      'establishment',
      'list',
      InstitutionList,
      Record<string, unknown>
    >
      controlledListKey="establishment"
      view="list"
      locale={locale}
      columns={establishmentsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="establishments"
    />
  );
};
