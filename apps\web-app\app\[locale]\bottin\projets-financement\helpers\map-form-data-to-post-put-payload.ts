import { getPostLocalizedValue } from '@/helpers/resources.helpers';
import type { FinancingProjectFormSchema } from '@/schemas/bottin/financing-project-form-schema';
import type { EntityPayload } from '@/types/bottin/directory';
import type { ProjectPostPayload } from '@/types/bottin/project';

export const mapProjectBaseForm = (
  formData: FinancingProjectFormSchema,
): ProjectPostPayload => ({
  persons: formData.associateResearchers.map(({ value }) => value),
  equipements: formData.purchasedEquipment.map(({ value }) => value),
  infrastructures: formData.financedInfrastructures.map(({ value }) => value),
  titres: getPostLocalizedValue(formData.name),
  titulaire: { id: formData.principalResearcher?.value },
  descriptions: getPostLocalizedValue(formData.description),
  typeProjet: { id: formData.financingProjectType?.value },
  synchroId: formData.synchroId,
  fciId: formData.identificationFci,
  projetFinancementNumeros: [],
});

export const mapFormDataToPostPayload = (
  formData: FinancingProjectFormSchema,
): EntityPayload<'fundingProject'> => {
  const baseStructure = mapProjectBaseForm(formData);

  return {
    ...baseStructure,
    projetFinancementId: formData.id
      ? Number.parseInt(formData.id.toString(), 10)
      : 0,
    anneeObtention: formData.obtainingYear ?? 0,
    dateFin: formData.closingDate
      ? new Date(formData.closingDate).toISOString()
      : '',
  };
};
