import { PgDatabaseLayer } from '@rie/postgres-db';
import { EquipmentsRepositoryLive } from '@rie/repositories';
import { EquipmentsServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const EquipmentsServicesLayer = Layer.mergeAll(
  EquipmentsRepositoryLive.Default,
  EquipmentsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const EquipmentsRuntime = ManagedRuntime.make(EquipmentsServicesLayer);
