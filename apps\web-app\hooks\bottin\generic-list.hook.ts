import {
  getGenericByIdOptions,
  getGenericListOptions,
} from '@/hooks/bottin/generic-list.options';
import type { ControlledListKey } from '@/types/common';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { useQuery } from '@tanstack/react-query';

export const useGetGenericList = <
  Key extends ControlledListKey,
  View extends CollectionViewParamType['view'],
>({
  controlledListKey,
  view,
}: {
  controlledListKey: Key;
  view: View;
}) => {
  return useQuery(getGenericListOptions({ controlledListKey, view }));
};

interface GetGenericByIdParams<Key extends ControlledListKey> {
  controlledListKey: Key;
  id: string;
  view: ResourceViewType;
}

export const useGetGenericById = <
  Key extends ControlledListKey,
  View extends ResourceViewType,
>({
  controlledListKey,
  id,
  view,
}: GetGenericByIdParams<Key> & { view: View }) => {
  return useQuery(getGenericByIdOptions({ controlledListKey, id, view }));
};
