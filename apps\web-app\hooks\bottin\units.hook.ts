import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import type { UnitFormSchema } from '@/schemas/bottin/unit-form-schema';
import {
  createGeneric,
  deleteGeneric,
  updateGeneric,
} from '@/services/bottin/generic-list.service';
import type { UnitEdit } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateUnit = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<UnitEdit, Error, UnitFormSchema>({
    mutationFn: async (payload) =>
      (await createGeneric({
        controlledListKey: 'unit',
        payload,
      })) as unknown as UnitEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'unit', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Unit created successfully',
        variant: 'success',
      });
      router.push('/bottin/unites');
    },
  });
};

export const useUpdateUnit = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<UnitEdit, Error, { id: string; payload: UnitFormSchema }>({
    mutationFn: async ({ id, payload }) =>
      (await updateGeneric({
        controlledListKey: 'unit',
        id,
        payload,
      })) as unknown as UnitEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'unit', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Unit updated successfully',
        variant: 'success',
      });
    },
  });
};

export const useDeleteUnit = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<void, Error, string>({
    mutationFn: async (id: string) => {
      await deleteGeneric({ controlledListKey: 'unit', id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'unit', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Unit deleted successfully',
        variant: 'success',
      });
    },
  });
};
