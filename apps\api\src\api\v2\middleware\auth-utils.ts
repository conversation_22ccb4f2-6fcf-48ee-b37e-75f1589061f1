import { ApiHttpClientLive } from '@/infrastructure/http/http-client.live';
import { HttpClientRequest, HttpClientResponse } from '@effect/platform';
import { ConfigLive } from '@rie/config';
import { Schemas } from '@rie/domain';
import { Data, Effect, Schedule } from 'effect';

// Tagged errors for better error handling
export class NoTokenError extends Data.TaggedError('NoTokenError')<{
  message: string;
}> {}

export class InvalidTokenError extends Data.TaggedError('InvalidTokenError')<{
  message: string;
}> {}

export const makeEffect =
  <S = never>() =>
  <E, R>(effect: Effect.Effect<S, E, R>) =>
    effect;

export const getTokenFromHeader = (authHeader?: string) =>
  makeEffect<string>()(
    !authHeader || !authHeader.startsWith('Bearer ')
      ? Effect.fail(
          new NoTokenError({
            message: 'Missing Authorization header.',
          }),
        )
      : Effect.succeed(authHeader.split(' ')[1]),
  );

export const getUserPermissions = (token: string) => {
  const makeUserInfoRequest = Effect.gen(function* (_) {
    const httpClientDefault = yield* ApiHttpClientLive;
    const httpClient = yield* httpClientDefault.makeHttpClient;
    const userInfoRequest = HttpClientRequest.get('/auth/userinfo').pipe(
      HttpClientRequest.bearerToken(token),
    );

    return yield* httpClient.execute(userInfoRequest).pipe(
      Effect.catchTags({
        RequestError: () =>
          Effect.fail(new NoTokenError({ message: 'Invalid token' })),
      }),
      Effect.flatMap(
        HttpClientResponse.schemaBodyJson(Schemas.UserSchema, {
          onExcessProperty: 'ignore',
        }),
      ),
      Effect.catchTag('ParseError', () =>
        Effect.fail(new InvalidTokenError({ message: 'Invalid token' })),
      ),
      Effect.tap((value) => value),
    );
  });

  return makeUserInfoRequest.pipe(
    Effect.provide(ApiHttpClientLive.Default),
    Effect.provide(ConfigLive.Default),
    Effect.withSpan('getUserPermissions'),
    Effect.scoped,
    Effect.retry({
      schedule: Schedule.exponential('200 millis', 2),
      times: 2,
    }),
  );
};

export const getMessageFromCause = (
  squashedError: unknown,
  defaultMessage: string,
): string => {
  if (typeof squashedError === 'string') {
    return squashedError;
  }

  if (typeof squashedError === 'object' && squashedError !== null) {
    return 'message' in squashedError &&
      typeof squashedError.message === 'string'
      ? squashedError.message
      : defaultMessage;
  }
  return defaultMessage;
};
