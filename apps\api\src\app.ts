import { createUserContextMiddleware } from '@/api/v2/middleware/user-context-middleware';
import apiRouter from '@/api/v2/routes';
import { ConfigLive } from '@rie/config';
import { LoggerLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Layer from 'effect/Layer';
import * as Redacted from 'effect/Redacted';
import { cors } from 'hono/cors';

import { createAuthMiddleware } from '@/api/v2/middleware/auth-middleware';
import { Hono } from 'hono';
import { except } from 'hono/combine';
import { etag } from 'hono/etag';
import { logger } from 'hono/logger';
import { secureHeaders } from 'hono/secure-headers';

const config = Effect.runSync(
  Effect.provide(
    Effect.flatMap(ConfigLive, (config) => Effect.succeed(config)),
    ConfigLive.Default,
  ),
);
const app = new Hono();
app.use(
  '*',
  cors({
    origin: [Redacted.value(config.NEXT_PUBLIC_ORIGIN_URL)],
    allowHeaders: ['Content-Type', 'Authorization', 'Cookie'],
    allowMethods: ['POST', 'PUT', 'DELETE', 'GET', 'OPTIONS', 'HEAD'],
    exposeHeaders: ['Set-Cookie', 'Content-Length'],
    maxAge: 600,
    credentials: true,
  }),
);
app.use('*', logger());
app.use('*', etag());
app.use('*', secureHeaders());
app.use('*', createUserContextMiddleware());

app.use(
  '/api/*',
  except(
    [
      '/api/doc',
      '/api/openapi',
      '/api/v2/auth/*', // Exclude all auth routes
      '/api/v2/auth', // Exclude the auth base route
    ],
    createAuthMiddleware(),
  ),
);
app.route('/api', apiRouter);

export const server = Effect.gen(function* (_) {
  const { PORT } = yield* ConfigLive;
  yield* Effect.log(`Server running on port ${PORT}`);
  return { fetch: app.fetch, port: PORT };
}).pipe(Effect.annotateLogs({ file: 'app.ts' }));

const RuntimeLayer = Layer.mergeAll(ConfigLive.Default, LoggerLive);
export default Effect.runSync(
  server.pipe(Effect.provide(RuntimeLayer), Effect.withSpan('ServerStartup')),
);
