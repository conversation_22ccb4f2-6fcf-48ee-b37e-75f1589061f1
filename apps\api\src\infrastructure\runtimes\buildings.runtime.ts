import { PgDatabaseLayer } from '@rie/postgres-db';
import { BuildingsRepositoryLive } from '@rie/repositories';
import { BuildingsServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const BuildingsServicesLayer = Layer.mergeAll(
  BuildingsRepositoryLive.Default,
  BuildingsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const BuildingsRuntime = ManagedRuntime.make(BuildingsServicesLayer);
