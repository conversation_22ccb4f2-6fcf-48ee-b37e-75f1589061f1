import type { Namespace } from '@/locales/en';
import type { Entries } from '@/types/utilities';
import { getTranslations } from 'next-intl/server';

type GetFormSectionsArgs<T extends string> = {
  resourceName: Namespace;
  sections: Record<T, { key: string; order: number }>;
};

export const getFormSections = async <T extends string>({
  resourceName,
  sections,
}: GetFormSectionsArgs<T>) => {
  const t = await getTranslations(`${resourceName}.form.sections`);
  return (Object.entries(sections) as Entries<typeof sections>)
    .toSorted(([, { order: orderA }], [, { order: orderB }]) => orderA - orderB)
    .reduce<Record<T, string>>(
      (acc, [key, value]) => {
        acc[key] = t(`${value.key}.title`) as string;

        return acc;
      },
      {} as Record<T, string>,
    );
};
