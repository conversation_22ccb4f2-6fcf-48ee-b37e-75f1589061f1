import { useUserHasPermissionTo } from '@/hooks/useUserHasPermissionTo';
import type { PermissionAction, ResourceType } from '@rie/domain/types';
import type { PropsWithChildren } from 'react';

type PermissionGateProps = {
  action: PermissionAction;
  resourceType: ResourceType;
  resourceId?: string;
};

export const PermissionGate = ({
  children,
  resourceId,
  resourceType,
  action,
}: PropsWithChildren<PermissionGateProps>) => {
  const { permissionStatus } = useUserHasPermissionTo({
    resourceId,
    resourceType,
    action,
  });

  return permissionStatus === 'granted' ? <>{children}</> : null;
};
