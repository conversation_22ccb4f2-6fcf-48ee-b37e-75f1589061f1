'use client';

import { TableLoadingResource } from '@/components/table-loading-resource/TableLoadingResource';
import { getPinningStyles } from '@/helpers/table';
import { Button } from '@/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/ui/table';
import {
  type ColumnDef,
  type Table as TanstackTable,
  flexRender,
} from '@tanstack/react-table';
import { useTranslations } from 'next-intl';
import * as React from 'react';
import { LuPin, LuPinOff } from 'react-icons/lu';

interface DataTableProps<TData extends object, TValue extends object> {
  columns: ColumnDef<TData, TValue>[];
  table: TanstackTable<TData>;
  onColumnPinAction: () => void;
  onColumnUnPinAction: () => void;
  isLoading: boolean;
}

export const DataTable = <TData extends object, TValue extends object>({
  columns,
  table,
  onColumnPinAction,
  onColumnUnPinAction,
  isLoading,
}: DataTableProps<TData, TValue>) => {
  const t = useTranslations('common');

  return (
    <Table
      className="mb-2 min-w-full border"
      style={{
        width: table.getTotalSize(),
      }}
    >
      <TableHeader>
        {table.getHeaderGroups().map((headerGroup) => (
          <TableRow className="divide-x divide-gray-200" key={headerGroup.id}>
            {headerGroup.headers.map((header, index) => {
              const { column } = header;
              return (
                <TableHead
                  className="bg-white"
                  key={header.id}
                  style={
                    column.getIsPinned()
                      ? { ...getPinningStyles(column, index) }
                      : {
                          width: `${column.getSize()}px`,
                        }
                  }
                >
                  <div className="flex items-center gap-3">
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          column.columnDef.header,
                          header.getContext(),
                        )}
                    {!header.isPlaceholder &&
                      column.getCanPin() &&
                      (column.getIsPinned() === 'left' ? (
                        <Button
                          onClick={onColumnUnPinAction}
                          size="icon"
                          variant="ghost"
                        >
                          <LuPinOff className="hover:stroke-accent-foreground" />
                        </Button>
                      ) : (
                        <Button
                          onClick={onColumnPinAction}
                          size="icon"
                          variant="ghost"
                        >
                          <LuPin className="hover:stroke-accent-foreground" />
                        </Button>
                      ))}
                  </div>
                </TableHead>
              );
            })}
          </TableRow>
        ))}
      </TableHeader>
      <TableBody>
        {isLoading ? (
          <TableRow>
            <TableLoadingResource colSpan={columns.length} />
          </TableRow>
        ) : table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row, index) => (
            <TableRow
              className="divide-x divide-gray-200"
              data-state={row.getIsSelected() && 'selected'}
              key={row.id}
            >
              {row.getVisibleCells().map((cell) => (
                <TableCell
                  key={cell.id}
                  style={
                    cell.column.getIsPinned()
                      ? { ...getPinningStyles(cell.column, index) }
                      : {
                          width: `${cell.column.getSize()}px`,
                        }
                  }
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell className="h-24 text-center" colSpan={columns.length}>
              {t('noResults')}
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};
