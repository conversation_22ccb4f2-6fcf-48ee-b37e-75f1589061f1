import {
  type GenericListResultType,
  type GenericResourceResultType,
  getGenericById,
  getGenericList,
} from '@/services/bottin/generic-list.service';
import type { ControlledListKey } from '@/types/common';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { queryOptions } from '@tanstack/react-query';

export const getGenericListOptions = <
  Key extends ControlledListKey,
  View extends CollectionViewParamType['view'],
>({
  controlledListKey,
  view,
}: {
  controlledListKey: Key;
  view: View;
}) => {
  return queryOptions<GenericListResultType<Key, View>>({
    queryFn: () => getGenericList({ controlledListKey, view }),
    queryKey: ['bottin', controlledListKey, { view }],
  });
};

interface GetGenericByIdParams<Key extends ControlledListKey> {
  controlledListKey: Key;
  id: string;
  view: ResourceViewType;
}

export const getGenericByIdOptions = <
  Key extends ControlledListKey,
  View extends ResourceViewType,
>({
  controlledListKey,
  id,
  view,
}: GetGenericByIdParams<Key> & { view: View }) => {
  return queryOptions<GenericResourceResultType<Key, View>>({
    queryFn: () => getGenericById({ controlledListKey, id, view }),
    queryKey: ['bottin', controlledListKey, id, view],
    enabled: !!id,
  });
};
