import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  LocaleRepositoryLive,
  ServiceOfferRepositoryLive,
} from '@rie/repositories';
import { ServiceOfferServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const ServicesLayer = Layer.mergeAll(
  ServiceOfferRepositoryLive.Default,
  LocaleRepositoryLive.Default,
  ServiceOfferServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

// Create a combined database layer

export const EndpointRuntime = ManagedRuntime.make(ServicesLayer);
