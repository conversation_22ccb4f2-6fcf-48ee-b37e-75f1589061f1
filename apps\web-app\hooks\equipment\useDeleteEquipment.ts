import { useToast } from '@/components/hooks/use-toast';
import { deleteEquipment } from '@/services/equipments';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';

export const useDeleteEquipment = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  const tCommon = useTranslations('common');
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => deleteEquipment(id),
    onError: (error) => {
      let errorMessage = tCommon('notifications.errors.onDeleteResource', {
        resource: tCommon('resources.equipment'),
      });

      if (error instanceof Error) {
        errorMessage = error.message;
      } else {
        errorMessage = `${tCommon('notifications.errors.generic')}: ${String(error)}`;
      }
      toast({
        description: errorMessage,
        title: tCommon('notifications.errorTitle'),
        variant: 'destructive',
      });
    },
    onSuccess: () => {
      toast({
        description: tCommon('notifications.success', {
          action: tCommon('actions.deleted'),
          resource: tCommon('resources.equipment'),
        }),
        title: tCommon('notifications.successTitle'),
        variant: 'success',
      });

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['allEquipments'] });
      queryClient.invalidateQueries({ queryKey: ['infiniteEquipments'] });
      queryClient.invalidateQueries({ queryKey: ['equipmentsList'] });

      if (onSuccess) {
        onSuccess();
      }
    },
  });
};
