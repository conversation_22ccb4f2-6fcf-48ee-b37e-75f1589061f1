# User Permissions API Documentation

This document describes the User Permissions API endpoints for managing user roles and checking permissions.

## Base URL
```
/api/v2/users
```

**Note**: User permissions functionality has been moved from `/api/v2/user-permissions` to `/api/v2/users` for better REST API consistency. All routes are now user-centric.

## Endpoints

### 1. Assign Role to User
**POST** `/:userId/roles`

Assigns a role to a user with optional context (institution, unit, infrastructure, or none).

#### Request Body
```json
{
  "roleId": "string",
  "resourceType": "domainEnum", // optional, defaults to "NULL"
  "resourceId": "string", // optional, required if domain is specified
  "grantedBy": "string" // optional, ID of user granting the role
}
```

**Note**: The `userId` is now passed as a path parameter instead of in the request body.

#### Response (201 Created)
```json
{
  "userId": "string",
  "roleId": "string",
  "resourceType": "domainEnum | null",
  "resourceId": "string | null",
  "grantedBy": "string | null",
  "createdAt": "string"
}
```

#### Example
```bash
curl -X POST /api/v2/users/user-123/roles \
  -H "Content-Type: application/json" \
  -d '{
    "roleId": "equipment-manager",
    "resourceType": "infrastructure",
    "resourceId": "lab-001",
    "grantedBy": "admin-456"
  }'
```

### 2. Remove Role from User
**DELETE** `/:userId/roles/:roleId`

Removes a role assignment from a user.

**Note**: Both `userId` and `roleId` are now passed as path parameters following REST conventions. Context parameters are no longer needed since roles are context-specific by design.

#### Example
```bash
curl -X DELETE /api/v2/users/user-123/roles/equipment-manager \
  -H "Authorization: Bearer <your-jwt-token>"
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Role removed successfully"
}
```

### 3. Check User Permission
**POST** `/:userId/permissions/check`

Checks if a user has a specific permission for a domain/action combination.

#### Request Body
```json
{
  "domain": "equipment" | "infrastructure" | "institution" | "unit" | ..., // see permission domains
  "action": "create" | "read" | "update" | "delete",
  "resourceId": "string", // optional, specific resource ID
}
```

**Note**: The `userId` is now passed as a path parameter instead of in the request body.

#### Response (200 OK)
```json
{
  "hasPermission": true,
  "userId": "string",
  "domain": "equipment",
  "action": "read",
  "resourceId": "equipment-123",
}
```

### 4. Check User Access to Resource
**POST** `/:userId/access/check`

Checks if a user has access to a specific resource through the hierarchical access system.

#### Request Body
```json
{
  "resourceType": "institution" | "unit" | "infrastructure" | "equipment",
  "resourceId": "string"
}
```

**Note**: The `userId` is now passed as a path parameter instead of in the request body.

#### Response (200 OK)
```json
{
  "hasAccess": true,
  "userId": "string",
  "resourceType": "equipment",
  "resourceId": "equipment-123"
}
```

### 5. Get User Access Tree
**GET** `/:userId/access-tree`

Returns the complete access tree for a user, showing all resources they have access to.

#### Response (200 OK)
```json
{
  "institutions": ["inst-1", "inst-2"],
  "units": ["unit-1", "unit-2", "unit-3"],
  "infrastructures": ["infra-1", "infra-2"],
  "equipments": ["eq-1", "eq-2", "eq-3"]
}
```

### 6. Get User Permissions for Resource
**GET** `/:userId/permissions/:resourceType/:resourceId`

Returns all permissions a user has for a specific resource.

#### Parameters
- `userId`: User ID
- `resourceType`: Type of resource (institution, unit, infrastructure, equipment)
- `resourceId`: ID of the resource

#### Response (200 OK)
```json
{
  "permissions": [
    {
      "id": "perm-1",
      "domain": "equipment",
      "action": "read",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    },
    {
      "id": "perm-2", 
      "domain": "equipment",
      "action": "update",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "userId": "user-123",
  "resourceType": "equipment",
  "resourceId": "equipment-456"
}
```

## Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid request data"
}
```

### 404 Not Found
```json
{
  "error": "User not found"
}
```

### 409 Conflict
```json
{
  "error": "Role already assigned to user"
}
```

### 500 Internal Server Error
```json
{
  "error": "An error occurred"
}
```

## Permission Domains

Available permission domains:
- `address`
- `applicationSector`
- `building`
- `campus`
- `equipment`
- `excellenceHub`
- `fundingProject`
- `infrastructure`
- `innovationLab`
- `institution`
- `media`
- `people`
- `researchField`
- `room`
- `serviceContract`
- `serviceOffer`
- `technique`
- `unit`
- `vendor`
- `visibility`

## Permission Actions

Available permission actions:
- `create`
- `read`
- `update`
- `delete`

## Context Types

Available context types for role assignments:
- `institution`: Role applies to a specific institution
- `unit`: Role applies to a specific unit
- `infrastructure`: Role applies to a specific infrastructure
- `equipment`: Role applies to a specific equipment
- `none`: Role applies globally (no context)

## Hierarchical Access

The system implements hierarchical access where:
- **Institution role** → Access to all units, infrastructures, and equipment within that institution
- **Unit role** → Access to all infrastructures and equipment within that unit
- **Infrastructure role** → Access to all equipment within that infrastructure
- **Equipment role** → Access to that specific equipment only

## Authentication

All endpoints require authentication. Include the bearer token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```
