import { getAllEquipments } from '@/services/equipments.service';
import type { CollectionViewParamType } from '@rie/domain/types';
import { queryOptions, useQuery } from '@tanstack/react-query';

type EquipmentsView = CollectionViewParamType['view'] | 'grid';

export const getEquipmentsOptions = <View extends EquipmentsView>({
  view,
}: { view: EquipmentsView }) =>
  queryOptions({
    queryKey: ['v2', 'equipments', { view }],
    queryFn: () => getAllEquipments<View>({ view }),
  });

export const useGetEquipments = <View extends EquipmentsView>({
  view,
}: { view: EquipmentsView }) => useQuery(getEquipmentsOptions<View>({ view }));
