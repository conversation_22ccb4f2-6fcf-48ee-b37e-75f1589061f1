import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import { DbEquipmentSchema, EquipmentListSchema } from '../schemas';
import type { CollectionViewType, ResourceViewType } from '../types';

export const DbEquipmentToList = Schema.transformOrFail(
  DbEquipmentSchema,
  EquipmentListSchema,
  {
    strict: false,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          const translations = raw.translations ?? [];
          const frName = translations.find((t) => t.locale === 'fr')?.name;
          const enName = translations.find((t) => t.locale === 'en')?.name;
          const defaultName =
            frName || enName || translations?.[0]?.name || raw.id;

          type Translation = { locale: string; name?: string | null };
          type TranslatedEntity = { translations?: Array<Translation> | null };

          const typeTranslations = ((raw as Partial<{ type: TranslatedEntity }>)
            .type?.translations ?? []) as Array<Translation>;
          const typeName =
            typeTranslations.find((t) => t.locale === 'fr')?.name ||
            typeTranslations.find((t) => t.locale === 'en')?.name ||
            typeTranslations?.[0]?.name ||
            null;

          const statusTranslations = ((
            raw as Partial<{ status: TranslatedEntity }>
          ).status?.translations ?? []) as Array<Translation>;
          const statusText =
            statusTranslations.find((t) => t.locale === 'fr')?.name ||
            statusTranslations.find((t) => t.locale === 'en')?.name ||
            statusTranslations?.[0]?.name ||
            null;

          const parentInfraTranslations = ((
            raw as Partial<{ infrastructure: TranslatedEntity }>
          ).infrastructure?.translations ?? []) as Array<Translation>;
          const parentInfrastructure =
            parentInfraTranslations.find((t: Translation) => t.locale === 'fr')
              ?.name ||
            parentInfraTranslations.find((t: Translation) => t.locale === 'en')
              ?.name ||
            parentInfraTranslations?.[0]?.name ||
            null;

          // Get manufacturer name
          const manufacturerTranslations = ((
            raw as Partial<{ manufacturer: TranslatedEntity }>
          ).manufacturer?.translations ?? []) as Array<Translation>;
          const manufacturerName =
            manufacturerTranslations.find((t) => t.locale === 'fr')?.name ||
            manufacturerTranslations.find((t) => t.locale === 'en')?.name ||
            manufacturerTranslations?.[0]?.name ||
            null;

          // Get address/location information
          type AddressInfo = {
            civicAddress?: {
              street1?: string | null;
              city?: string | null;
              state?: string | null;
              postalCode?: string | null;
            } | null;
            campusAddress?: {
              room_id?: string | null;
            } | null;
          };
          const addressInfo = (raw as Partial<{ address: AddressInfo }>)
            .address;
          let location = null;
          if (addressInfo?.civicAddress) {
            const civic = addressInfo.civicAddress;
            location = [
              civic.street1,
              civic.city,
              civic.state,
              civic.postalCode,
            ]
              .filter(Boolean)
              .join(', ');
          } else if (addressInfo?.campusAddress) {
            // Handle campus address if available
            const campus = addressInfo.campusAddress;
            location = campus.room_id ? `Room ${campus.room_id}` : 'Campus';
          }

          // Get equipment categories
          const categories =
            (
              raw as Partial<{
                categories: Array<{ translations?: Array<Translation> }>;
              }>
            ).categories ?? [];
          const equipmentCategories = categories
            .map((category) => {
              const categoryTranslations = category.translations ?? [];
              return (
                categoryTranslations.find((t) => t.locale === 'fr')?.name ||
                categoryTranslations.find((t) => t.locale === 'en')?.name ||
                categoryTranslations?.[0]?.name ||
                'Unknown Category'
              );
            })
            .filter(Boolean);

          return {
            id: raw.id,
            name: String(defaultName ?? raw.id ?? ''),
            type: typeName,
            status: statusText, // Use statusText as status for consistency
            statusText,
            infrastructureId: raw.infrastructureId ?? null,
            parentInfrastructure,
            location,
            manufacturer: manufacturerName,
            model: raw.model ?? null,
            equipmentCategories:
              equipmentCategories.length > 0 ? equipmentCategories : null,
            updatedAt: raw.updatedAt ?? null,
          };
        },
        catch: (error) =>
          new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse equipment for list view',
          ),
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Type for collection data (minimal fields)
type EquipmentCollectionItem = {
  id: string;
  model: string | null;
  updatedAt: string;
  infrastructureId: string;
  translations: Array<{
    id: string;
    locale: string;
    name: string | null;
  }>;
  type: {
    id: string;
    translations: Array<{
      locale: string;
      name: string | null;
    }>;
  } | null;
  status: {
    id: string;
    translations: Array<{
      locale: string;
      name: string | null;
    }>;
  } | null;
  manufacturer: {
    id: string;
    translations: Array<{
      locale: string;
      name: string | null;
    }>;
  } | null;
  infrastructure: {
    id: string;
    translations: Array<{
      locale: string;
      name: string | null;
    }>;
  };
};

// Type guard to check if data is collection data
function isCollectionData(row: unknown): row is EquipmentCollectionItem {
  return typeof row === 'object' && row !== null && !('createdAt' in row);
}

export const dbEquipmentsToCollection = (
  rows: Array<unknown>,
  _view: CollectionViewType,
) => {
  // Check if this is collection data (minimal fields) or full data
  const hasCollectionData =
    rows.length > 0 && rows[0] && isCollectionData(rows[0]);

  if (hasCollectionData) {
    // Handle collection data with minimal fields
    return (rows as Array<EquipmentCollectionItem>).map((row) => {
      const translations = row.translations ?? [];
      const frName = translations.find((t) => t.locale === 'fr')?.name;
      const enName = translations.find((t) => t.locale === 'en')?.name;
      const defaultName = frName || enName || translations?.[0]?.name || row.id;

      const typeTranslations = row.type?.translations ?? [];
      const typeName =
        typeTranslations.find((t) => t.locale === 'fr')?.name ||
        typeTranslations.find((t) => t.locale === 'en')?.name ||
        typeTranslations?.[0]?.name ||
        null;

      const statusTranslations = row.status?.translations ?? [];
      const statusName =
        statusTranslations.find((t) => t.locale === 'fr')?.name ||
        statusTranslations.find((t) => t.locale === 'en')?.name ||
        statusTranslations?.[0]?.name ||
        null;

      const manufacturerTranslations = row.manufacturer?.translations ?? [];
      const manufacturerName =
        manufacturerTranslations.find((t) => t.locale === 'fr')?.name ||
        manufacturerTranslations.find((t) => t.locale === 'en')?.name ||
        manufacturerTranslations?.[0]?.name ||
        null;

      const infrastructureTranslations = row.infrastructure?.translations ?? [];
      const infrastructureName =
        infrastructureTranslations.find((t) => t.locale === 'fr')?.name ||
        infrastructureTranslations.find((t) => t.locale === 'en')?.name ||
        infrastructureTranslations?.[0]?.name ||
        null;

      return {
        id: row.id,
        name: defaultName,
        type: typeName,
        status: statusName,
        statusText: statusName,
        infrastructureId: row.infrastructureId,
        parentInfrastructure: infrastructureName,
        location: null, // Not available in collection data
        manufacturer: manufacturerName,
        model: row.model,
        equipmentCategories: null, // Not available in collection data
        updatedAt: row.updatedAt,
      };
    });
  }

  // Handle full data using the schema transformer
  return (rows as Array<Schema.Schema.Type<typeof DbEquipmentSchema>>).map(
    (row) => Schema.decodeUnknownSync(DbEquipmentToList)(row),
  );
};

export const dbEquipmentToResource = (
  row: Schema.Schema.Type<typeof DbEquipmentSchema>,
  view: ResourceViewType,
) => {
  // For now return list shape for detail as well
  if (view === 'detail') {
    return Schema.decodeUnknownSync(DbEquipmentToList)(row);
  }
  return Schema.decodeUnknownSync(DbEquipmentToList)(row);
};
