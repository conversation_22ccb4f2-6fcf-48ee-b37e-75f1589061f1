import { mapFormAddressToAddressPayload } from '@/helpers/form-mappers';
import { getPostLocalizedValue } from '@/helpers/resources.helpers';
import type { ManufacturerFormSchema } from '@/schemas/bottin/manufacturer-form-schema';
import type { EntityPayload } from '@/types/bottin/directory';
import type { ManufacturerPostPayload } from '@/types/bottin/manufacturer';
import type { AddressPayload } from '@/types/common';

export const mapManufacturerBaseForm = (
  formData: ManufacturerFormSchema,
): ManufacturerPostPayload => ({
  names: getPostLocalizedValue(formData.name),
  pseudonymes: getPostLocalizedValue(formData.alias),
  ...(formData.phones &&
    formData.phones.length > 0 && {
      telephones: formData.phones.map((phoneNumber) => ({
        descriptions: getPostLocalizedValue(phoneNumber.description),
        number: phoneNumber.phone?.toString() || '',
      })),
    }),
  ...(formData.dateEnd && {
    dateEnd: formData.dateEnd.toISOString(),
  }),
});

export function mapFormDataToPostPutPayload(
  formData: ManufacturerFormSchema,
): EntityPayload<'vendor'> {
  const baseStructure = mapManufacturerBaseForm(formData);
  const manufacturerReception = formData.contacts
    .map(mapFormAddressToAddressPayload)
    .filter((v): v is AddressPayload => v !== null);

  return { ...baseStructure, emplacements: manufacturerReception };
}
