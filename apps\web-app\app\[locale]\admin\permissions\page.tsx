import { PermissionsList } from '@/app/[locale]/admin/permissions/list-permissions';
import { getQueryClientOptions } from '@/constants/query-client';
import { getAllPermissionsOptions } from '@/hooks/admin/permissions/permissions.options';
import { redirect } from '@/lib/navigation';
import type { BasePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';

export default async function PermissionsPage({ params }: BasePageParams) {
  const { locale } = await params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: '/admin/permissions' },
      },
      locale,
    });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));
  await queryClient.prefetchQuery(getAllPermissionsOptions({ view: 'list' }));

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <PermissionsList locale={locale} />
    </HydrationBoundary>
  );
}
