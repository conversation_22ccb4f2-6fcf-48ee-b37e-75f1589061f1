import { FieldWithTranslations } from '@/app/[locale]/equipements/form/components/field-with-translations';
import { getFieldErrorMessage } from '@/app/[locale]/equipements/form/helpers';
import { useGetMeasurementUnits } from '@/app/[locale]/equipements/form/sections/description/measurement/hooks/useGetMeasurementUnits';
import { FieldInfo } from '@/components/FieldInfo';
import { ComboboxField } from '@/components/form-fields/combobox-field';
import { LabelTooltip } from '@/components/label-tooltip/label-tooltip';
import { useTranslatedField } from '@/hooks/useTranslatedField';
import type { EquipmentFormSchema } from '@/schemas/equipment/equipment-form-schema';
import { FormControl, FormField, FormItem, FormMessage } from '@/ui/form';
import { Input } from '@/ui/input';
import { useTranslations } from 'next-intl';
import { useFormContext } from 'react-hook-form';

type MeasurementProps = {
  index: number;
};

export const MeasurementField = ({ index }: MeasurementProps) => {
  const tEquipments = useTranslations(
    'equipments.form.sections.description.measurement',
  );
  const tCommon = useTranslations('common');
  const { control, formState } = useFormContext<EquipmentFormSchema>();

  //TODO: use an infinite list or change the combobox
  const measurementUnits = useGetMeasurementUnits();

  const uncertaintyFields = useTranslatedField(
    control,
    `measurements.${index}.uncertainty`,
  );
  const uncertaintyError = getFieldErrorMessage(
    formState.errors,
    `measurements.${index}.uncertainty`,
  );
  const uncertaintyErrorMessage = uncertaintyError
    ? tEquipments(uncertaintyError)
    : undefined;
  return (
    <>
      <div className="flex flex-wrap items-center gap-4">
        <div className="min-w-[200px] flex-1">
          <FormField
            control={control}
            name={`measurements.${index}.measurementUncertainty`}
            render={({ field }) => (
              <FormItem>
                <LabelTooltip
                  label={tEquipments('fields.measurementUncertainty.label')}
                />
                <FormControl>
                  <Input className="!mt-5" min={0} type="number" {...field} />
                </FormControl>
                <FieldInfo>
                  <FormMessage />
                </FieldInfo>
              </FormItem>
            )}
          />
        </div>
        <div className="min-w-[200px] flex-1">
          <ComboboxField
            clearErrorsOnChange={false}
            fieldLabel={tEquipments('fields.unit.label')}
            fieldName={`measurements.${index}.unit`}
            options={measurementUnits.map((unit) => ({
              label: unit.label,
              value: unit.id,
            }))}
            placeholder={tCommon('select')}
          />
        </div>
      </div>
      <FieldWithTranslations
        control={control}
        errorMessage={uncertaintyErrorMessage}
        fieldComponent={(field) => <Input {...field} />}
        fieldName={`measurements.${index}.uncertainty`}
        fields={uncertaintyFields.fields}
        label={(locale) =>
          tEquipments('fields.uncertainty.labelWithLocale', {
            locale: tCommon(locale),
          })
        }
        onAddTranslation={uncertaintyFields.handleAddTranslation}
        onRemoveTranslation={uncertaintyFields.handleRemoveTranslation}
      />
    </>
  );
};
