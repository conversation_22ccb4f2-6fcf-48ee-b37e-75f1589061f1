import { ListRoles } from '@/app/[locale]/admin/roles/list-roles';
import { getQueryClientOptions } from '@/constants/query-client';
import { getAllRolesOptions } from '@/hooks/admin/permissions/roles.options';
import { redirect } from '@/lib/navigation';
import type { BasePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';

export default async function RolesPage({ params }: BasePageParams) {
  const { locale } = await params;

  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({ href: { pathname: '/login' }, locale });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));
  await queryClient.prefetchQuery(getAllRolesOptions({ view: 'list' }));

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <ListRoles locale={locale} />
    </HydrationBoundary>
  );
}
