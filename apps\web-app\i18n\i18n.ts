import { IntlErrorCode } from 'next-intl';
import { getRequestConfig } from 'next-intl/server';

import { defaultLocale, locales } from './settings';

export default getRequestConfig(async ({ requestLocale }) => {
  let locale = await requestLocale;
  // Ensure that the incoming locale is valid
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  if (!locale || !locales.includes(locale as any)) {
    locale = defaultLocale;
  }

  const messages = {
    common: (await import(`../locales/${locale}/common.locale.json`)).default,
    contractService: (
      await import(`../locales/${locale}/contract-service.locale.json`)
    ).default,
    directory: (await import(`../locales/${locale}/directory.locale.json`))
      .default,
    equipments: (await import(`../locales/${locale}/equipments.locale.json`))
      .default,
    error: (await import(`../locales/${locale}/error.locale.json`)).default,
    establishments: (await import(`../locales/${locale}/directory.locale.json`))
      .default,
    facets: (await import(`../locales/${locale}/facets.locale.json`)).default,
    financingProjects: (
      await import(`../locales/${locale}/financing-project.locale.json`)
    ).default,
    forms: (await import(`../locales/${locale}/forms.locale.json`)).default,
    homePage: (await import(`../locales/${locale}/home-page.locale.json`))
      .default,
    infrastructures: (
      await import(`../locales/${locale}/infrastructures.locale.json`)
    ).default,
    manufacturers: (await import(`../locales/${locale}/directory.locale.json`))
      .default,
    navigation: (await import(`../locales/${locale}/navigation.locale.json`))
      .default,
    people: (await import(`../locales/${locale}/directory.locale.json`))
      .default,
    permissions: (await import(`../locales/${locale}/permission.locale.json`))
      .default,
    permissionsGroups: (
      await import(`../locales/${locale}/permissions-groups.locale.json`)
    ).default,
    profile: (await import(`../locales/${locale}/profile.locale.json`)).default,
    roles: (await import(`../locales/${locale}/roles.locale.json`)).default,
    rooms: (await import(`../locales/${locale}/rooms.locale.json`)).default,
    units: (await import(`../locales/${locale}/units.locale.json`)).default,
    user: (await import(`../locales/${locale}/user.locale.json`)).default,
    buildings: (await import(`../locales/${locale}/buildings.locale.json`))
      .default,
  };

  return {
    locale,
    getMessageFallback({ error, key, namespace }) {
      const path = [namespace, key].filter((part) => part != null).join('.');

      if (error.code === IntlErrorCode.MISSING_MESSAGE) {
        return `${path} is not yet translated`;
      }
      return `Please fix this message: ${path}`;
    },
    messages,
  };
});
