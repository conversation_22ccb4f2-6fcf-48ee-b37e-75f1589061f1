import { APIV2Error } from '@/services/api-v2-error';
import { getApiClient } from '@/services/client/api-client';
import type {
  CollectionViewParamType,
  Equipment,
  EquipmentList,
  ResourceViewType,
} from '@rie/domain/types';

export const getEquipmentsCount = async (): Promise<number> => {
  try {
    const client = await getApiClient();
    const data = await client
      .get('v2/equipments', { searchParams: { limit: 0 } })
      .json<{ count: number }>();
    return data.count;
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch equipments count');
  }
};

export const getAllEquipments = async ({ view }: CollectionViewParamType) => {
  try {
    const client = await getApiClient();
    return await client
      .get<EquipmentList[]>('v2/equipments', {
        searchParams: { view },
      })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch equipments');
  }
};

type GetEquipmentByIdParams = { id: string; view: ResourceViewType };

export const getEquipmentById = async ({
  id,
  view,
}: GetEquipmentByIdParams) => {
  try {
    const client = await getApiClient();
    return await client
      .get<Equipment>(`v2/equipments/${id}`, { searchParams: { view } })
      .json();
  } catch (error) {
    if (error instanceof APIV2Error) {
      throw error;
    }
    throw new Error('Failed to fetch equipment');
  }
};
