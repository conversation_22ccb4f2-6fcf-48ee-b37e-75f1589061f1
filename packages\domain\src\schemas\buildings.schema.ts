import {
  DbBuildingI18NSelectSchema,
  DbBuildingSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';
import {
  optionalFieldWth1500MaxLengthSchema,
  optionalFieldWth150MaxLengthSchema,
  requiredFieldWth150MaxLengthSchema,
} from './base.schema';
import { LocaleSchema } from './query.schema';

// — Translation input schema (define first as it's used in other schemas)
export const BuildingI18NInputSchema = Schema.Struct({
  locale: LocaleSchema,
  name: requiredFieldWth150MaxLengthSchema('Name'),
  description: optionalFieldWth1500MaxLengthSchema('Description'),
  otherNames: optionalFieldWth150MaxLengthSchema('Other Names'),
  acronyms: optionalFieldWth150MaxLengthSchema('Acronyms'),
});

// — DB Building schema (from repository data with relations)
export const DbBuildingSchema = Schema.Struct({
  id: Schema.String,
  campusId: Schema.NullishOr(Schema.String),
  civicAddressId: Schema.NullishOr(Schema.String),
  sadId: Schema.NullishOr(Schema.String),
  diId: Schema.NullishOr(Schema.String),
  createdAt: Schema.String,
  updatedAt: Schema.String,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(
    Schema.Struct({
      id: Schema.String,
      locale: Schema.String,
      name: Schema.NullishOr(Schema.String),
      description: Schema.NullishOr(Schema.String),
      otherNames: Schema.NullishOr(Schema.String),
    }),
  ),
  campus: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      translations: Schema.Array(
        Schema.Struct({
          locale: Schema.String,
          name: Schema.NullishOr(Schema.String),
        }),
      ),
    }),
  ),
});

// — Building List view schema
export const BuildingListSchema = Schema.Struct({
  id: Schema.String,
  civicAddressId: Schema.NullishOr(Schema.String),
  sadId: Schema.NullishOr(Schema.String),
  diId: Schema.NullishOr(Schema.String),
  createdAt: Schema.String,
  updatedAt: Schema.String,
  modifiedBy: Schema.NullishOr(Schema.String),
  name: Schema.NullishOr(Schema.String), // from default locale translation
  description: Schema.NullishOr(Schema.String),
  campus: Schema.NullishOr(
    Schema.Struct({
      id: Schema.String,
      text: Schema.String,
    }),
  ),
  otherNames: Schema.NullishOr(Schema.String), // from building translations
});

// — Building Select view schema
export const BuildingSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Building Edit view schema (form data format)
export const BuildingEditSchema = Schema.Struct({
  id: Schema.String,
  campusId: Schema.NullishOr(Schema.String),
  civicAddressId: Schema.NullishOr(Schema.String),
  sadId: Schema.NullishOr(Schema.String),
  diId: Schema.NullishOr(Schema.String),
  translations: Schema.Array(BuildingI18NInputSchema),
});

// — Building Form schema (for the frontend form)
export const BuildingFormSchema = Schema.Struct({
  id: Schema.optional(Schema.String),
  name: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      value: Schema.String,
    }),
  ),
  alias: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      value: Schema.String,
    }),
  ),
  campus: Schema.Struct({
    label: Schema.NullishOr(Schema.String),
    value: Schema.NullishOr(Schema.String),
  }),
  jurisdiction: Schema.Struct({
    label: Schema.NullishOr(Schema.String),
    value: Schema.NullishOr(Schema.String),
  }),
  civicAddresses: Schema.Array(
    Schema.Struct({
      city: Schema.String,
      countryCode: Schema.String,
      fullAddress: Schema.String,
      lat: Schema.String,
      lon: Schema.String,
      placeId: Schema.String,
      postalCode: Schema.String,
      state: Schema.String,
      streetName: Schema.String,
      streetNumber: Schema.String,
    }),
  ),
});

// — Building Detail view schema (same as list for now)
export const BuildingDetailSchema = BuildingListSchema;

// — Full Building shape
export const BuildingSchema = Schema.Struct({
  ...DbBuildingSelectSchema.omit('modifiedBy').fields,
  modifiedBy: Schema.NullishOr(Schema.String),
  translations: Schema.Array(DbBuildingI18NSelectSchema.omit('id', 'dataId')),
});

// — Building Input schema (create/update) - same as form schema to follow permissions-groups pattern
export const BuildingInputSchema = BuildingFormSchema;

// — DB Building Input schema (what gets sent to DB)
export const DBBuildingInputSchema = Schema.Struct({
  campusId: Schema.NullishOr(Schema.String),
  civicAddressId: Schema.NullishOr(Schema.String),
  sadId: Schema.NullishOr(Schema.String),
  diId: Schema.NullishOr(Schema.String),
  translations: Schema.Array(BuildingI18NInputSchema),
});
