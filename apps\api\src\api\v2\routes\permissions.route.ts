import { effectValidator } from '@hono/effect-validator';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

import { handleEffectError } from '@/api/v2/utils/error-handler';
import { PermissionsRuntime } from '@/infrastructure/runtimes/permissions.runtime';
import {
  CollectionViewParamSchema,
  PermissionDetailSchema,
  PermissionInputSchema,
  PermissionSelectSchema,
  ResourceIdSchema,
  ResourceQuerySchema,
  ResourceViewSchema,
} from '@rie/domain/schemas';
import { PermissionsServiceLive } from '@rie/services';

export const createPermissionRoute = describeRoute({
  description: 'Crée une permission',
  operationId: 'createPermission',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(PermissionInputSchema),
        example: {
          domain: 'equipment',
          action: 'read',
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(PermissionDetailSchema),
        },
      },
      description: 'Permission créée avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission non trouvée',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission déjà existante',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permissions'],
});

const getPermissionByIdRoute = describeRoute({
  description: 'Obtient une permission par son ID',
  operationId: 'getPermissionById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la permission',
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(ResourceViewSchema),
      description: 'Type de vue: detail ou edit',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(PermissionDetailSchema),
        },
      },
      description: 'Permission retournée avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permissions'],
});

export const updatePermissionRoute = describeRoute({
  description: 'Mettre à jour une permission',
  operationId: 'updatePermission',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la permission à mettre à jour',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(PermissionInputSchema),
        example: {
          domain: 'equipment',
          action: 'update',
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(PermissionDetailSchema),
        },
      },
      description: 'Permission mise à jour avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permissions'],
});

export const deletePermissionRoute = describeRoute({
  description: 'Supprime une permission',
  operationId: 'deletePermission', // Fixed the operationId from updatePermission to deletePermission
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la permission à supprimer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              success: Schema.Boolean,
              message: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission supprimée avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permissions'],
});

export const getAllPermissionsRoute = describeRoute({
  description: 'Obtient toutes les permissions',
  operationId: 'getAllPermissions',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue pour la collection (list ou select)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              Schema.Array(PermissionDetailSchema),
              Schema.Array(PermissionSelectSchema),
            ),
          ),
        },
      },
      description:
        'Permissions retournées avec succès. Le format dépend du paramètre "view": "list" retourne tous les champs (id, domain, action, createdAt, updatedAt), "select" retourne le format value/label pour les dropdowns.',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Permission non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permissions'],
});

const permissionsRoute = new Hono();

permissionsRoute.get(
  '/',
  getAllPermissionsRoute,
  effectValidator('query', CollectionViewParamSchema),
  async (ctx) => {
    const { view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const permissionsService = yield* PermissionsServiceLive;
      return yield* permissionsService.getAllPermissions({ view });
    });

    const permissions = await PermissionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, permissions);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(permissions)) {
      return ctx.json(permissions.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

permissionsRoute.get(
  '/:id',
  getPermissionByIdRoute,
  effectValidator('query', ResourceQuerySchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const { view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const permissionsService = yield* PermissionsServiceLive;
      return yield* permissionsService.getPermissionById({ id, view });
    });

    const permission = await PermissionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, permission);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(permission)) {
      return ctx.json(permission.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

permissionsRoute.post(
  '/',
  createPermissionRoute,
  effectValidator('json', PermissionInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const permissionsService = yield* PermissionsServiceLive;
      return yield* permissionsService.createPermission(body);
    });

    const maybePermission = await PermissionsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, maybePermission);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(maybePermission)) {
      return ctx.json(maybePermission.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

permissionsRoute.put(
  '/:id',
  updatePermissionRoute,
  effectValidator('json', PermissionInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const { domain, action } = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const permissionsService = yield* PermissionsServiceLive;
      return yield* permissionsService.updatePermission({
        id,
        domain,
        action,
      });
    });

    const permission = await PermissionsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, permission);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(permission)) {
      return ctx.json(permission.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

permissionsRoute.delete('/:id', deletePermissionRoute, async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const permissionsService = yield* PermissionsServiceLive;
    return yield* permissionsService.deletePermission(id);
  });

  const result = await PermissionsRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(result)) {
    return ctx.json({
      success: result.value,
      message: 'Permission deleted successfully',
    });
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

export default permissionsRoute;
