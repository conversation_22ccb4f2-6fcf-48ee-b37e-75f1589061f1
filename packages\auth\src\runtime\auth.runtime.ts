import { AuthServiceLive } from '@/services/auth.service';
import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  EquipmentsRepositoryLive,
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  RolesServiceLive,
  UserPermissionsServiceLive,
  UsersServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const AuthServicesLayer = Layer.mergeAll(
  UsersRepositoryLive.Default,
  UnitsRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  EquipmentsRepositoryLive.Default,
  UsersServiceLive.Default,
  AccessTreeServiceLive.Default,
  RolesServiceLive.Default,
  UserPermissionsServiceLive.Default,
  AuthServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const AuthRuntime = ManagedRuntime.make(AuthServicesLayer);
