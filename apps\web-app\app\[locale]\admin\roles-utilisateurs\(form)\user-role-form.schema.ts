import { SelectOptionSchema } from '@rie/domain/schemas';
import * as Schema from 'effect/Schema';

export const userRoleFormSchema = Schema.Struct({
  user: SelectOptionSchema,
  role: SelectOptionSchema,
  resourceType: SelectOptionSchema,
  resourceId: SelectOptionSchema,
}).pipe(
  Schema.filter(
    ({ resourceType, resourceId }) =>
      resourceType.value === '' ||
      (resourceType.value !== '' && resourceId.value !== ''),
    {
      message: () => 'Domain ID is required when a domain is selected',
    },
  ),
);

export type UserRoleFormSchema = Schema.Schema.Type<typeof userRoleFormSchema>;
