import { domainEnum, permissionActionEnum } from '@rie/db-schema/schemas';
import * as Schema from 'effect/Schema';

// Create custom action schema with filter for better error messages
const ActionSchema = Schema.String.pipe(
  Schema.filter(
    (input) =>
      permissionActionEnum.enumValues.includes(
        input as (typeof permissionActionEnum.enumValues)[number],
      ),
    {
      message: () =>
        `Invalid action. Must be one of: ${permissionActionEnum.enumValues.join(', ')}`,
    },
  ),
);

// Create custom domain schema with filter for better error messages
const DomainSchema = Schema.String.pipe(
  Schema.filter(
    (input) =>
      domainEnum.enumValues.includes(
        input as (typeof domainEnum.enumValues)[number],
      ),
    {
      message: () => 'Invalid domain. Please provide a valid domain.',
    },
  ),
);

// Use the same schema as the backend to ensure consistency
export const permissionFormSchema = Schema.Struct({
  action: Schema.Union(ActionSchema, Schema.Literal('')).pipe(
    Schema.nonEmptyString({ message: () => 'Action is required' }),
  ),
  domain: Schema.Union(DomainSchema, Schema.Literal('')).pipe(
    Schema.nonEmptyString({ message: () => 'Domain is required' }),
  ),
});

export type PermissionFormSchema = Schema.Schema.Type<
  typeof permissionFormSchema
>;
