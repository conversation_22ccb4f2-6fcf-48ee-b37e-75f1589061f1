import { PgDatabaseLayer } from '@rie/postgres-db';
import { FundingProjectsRepositoryLive } from '@rie/repositories';
import { FundingProjectsServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const FundingProjectsServicesLayer = Layer.mergeAll(
  FundingProjectsRepositoryLive.Default,
  FundingProjectsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const FundingProjectsRuntime = ManagedRuntime.make(
  FundingProjectsServicesLayer,
);
