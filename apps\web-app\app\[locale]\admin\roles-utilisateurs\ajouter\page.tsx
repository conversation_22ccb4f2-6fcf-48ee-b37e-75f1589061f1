import { UserRoleForm } from '@/app/[locale]/admin/roles-utilisateurs/(form)/user-role-form';
import { getQueryClientOptions } from '@/constants/query-client';
import { getAllPermissionsGroupsOptions } from '@/hooks/admin/permissions/permissions-groups.options';
import { getAllPermissionsOptions } from '@/hooks/admin/permissions/permissions.options';
import { getAllRolesOptions } from '@/hooks/admin/permissions/roles.options';
import { redirect } from '@/lib/navigation';
import type { BasePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import { QueryClient } from '@tanstack/react-query';
import { headers } from 'next/headers';

export default async function UtilisateursRolesPage({
  params,
}: BasePageParams) {
  const { locale } = await params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: '/admin/utilisateurs-roles/ajouter' },
      },
      locale,
    });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  await Promise.all([
    queryClient.prefetchQuery(
      getAllPermissionsOptions<'select'>({ view: 'select' }),
    ),
    queryClient.prefetchQuery(
      getAllPermissionsGroupsOptions<'select'>({ view: 'select' }),
    ),
    queryClient.prefetchQuery(getAllRolesOptions<'select'>({ view: 'select' })),
  ]);

  return (
    <div className="container mx-auto py-10">
      <UserRoleForm />
    </div>
  );
}
