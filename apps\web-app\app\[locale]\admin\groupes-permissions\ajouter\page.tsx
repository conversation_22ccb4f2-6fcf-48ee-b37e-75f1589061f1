import { NewPermissionsGroup } from '@/app/[locale]/admin/groupes-permissions/ajouter/new-permissions-group';
import { getQueryClientOptions } from '@/constants/query-client';
import { getAllPermissionsOptions } from '@/hooks/admin/permissions/permissions.options';
import { redirect } from '@/lib/navigation';
import type { BasePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';

export default async function PermissionsPage({ params }: BasePageParams) {
  const { locale } = await params;
  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: '/admin/groupes-permissions/ajouter' },
      },
      locale,
    });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  await queryClient.prefetchQuery(
    getAllPermissionsOptions<'select'>({ view: 'select' }),
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <div className="container mx-auto py-10">
        <NewPermissionsGroup />
      </div>
    </HydrationBoundary>
  );
}
