import {
  ACCESS_TOKEN_KEY,
  EXPIRES_IN_KEY,
  REFERER_KEY,
  REFRESH_TOKEN_KEY,
} from '@/constants/common';
import { env } from '@/env';
import dayjs from '@/lib/dayjs';
import { APIError } from '@/services/api-error';
import { validateAuthorizationCallback } from '@/services/utils/auth';
import { deleteCookie } from '@/services/utils/cookies';
import { cookies } from 'next/headers';

export const GET = async (req: Request) => {
  const { searchParams, protocol } = new URL(req.url);
  const code = searchParams.get('code');

  // Extract domain from NEXT_PUBLIC_ORIGIN_URL
  const originUrl = new URL(env.NEXT_PUBLIC_ORIGIN_URL);
  const cookieDomain = originUrl.hostname;
  const isSecure = protocol === 'https:';
  const basePath = env.NEXT_PUBLIC_BASE_PATH; // default to empty string if BASE_PATH is not defined

  if (!code) {
    throw APIError.fromError(Error('No code provided'));
  }

  const authInfo = await validateAuthorizationCallback(code);

  if (!authInfo) {
    throw APIError.fromError(Error('No auth info returned'));
  }

  const { accessToken, expiresIn, refreshToken } = authInfo;
  const cookieStore = await cookies();

  cookieStore.set({
    maxAge: expiresIn,
    name: ACCESS_TOKEN_KEY,
    path: basePath,
    sameSite: 'lax',
    secure: isSecure,
    value: accessToken,
    domain: cookieDomain,
  });

  if (refreshToken) {
    cookieStore.set({
      maxAge: expiresIn,
      name: REFRESH_TOKEN_KEY,
      sameSite: 'lax',
      secure: isSecure,
      path: basePath,
      value: refreshToken,
      domain: cookieDomain,
    });
  }

  if (expiresIn) {
    cookieStore.set({
      maxAge: expiresIn,
      name: EXPIRES_IN_KEY,
      path: basePath,
      sameSite: 'lax',
      secure: isSecure,
      value: dayjs().add(expiresIn, 's').format(),
      domain: cookieDomain,
    });
  }

  const referer = cookieStore.get(REFERER_KEY);
  // Remove the BASE_PATH from the referer.value
  const refererValue = referer?.value || '';
  const cleanedReferer = refererValue.replace(basePath, '');

  const redirectURL = `${env.NEXT_PUBLIC_ORIGIN_URL}${basePath}${cleanedReferer}`;

  await deleteCookie(REFERER_KEY);

  return Response.redirect(redirectURL);
};
