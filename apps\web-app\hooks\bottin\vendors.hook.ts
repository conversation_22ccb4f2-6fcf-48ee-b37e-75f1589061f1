import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import type { ManufacturerFormSchema } from '@/schemas/bottin/manufacturer-form-schema';
import {
  createGeneric,
  deleteGeneric,
  updateGeneric,
} from '@/services/bottin/generic-list.service';
import type { VendorEdit } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateVendor = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<VendorEdit, Error, ManufacturerFormSchema>({
    mutationFn: async (payload: ManufacturerFormSchema) => {
      return (await createGeneric({
        controlledListKey: 'supplier',
        payload,
      })) as unknown as VendorEdit;
    },
    onSuccess: (data: VendorEdit) => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'supplier', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Vendor created successfully',
        variant: 'success',
      });
      if (typeof (data as { id?: string }).id === 'string') {
        router.push('/bottin/manufacturiers');
      }
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to create vendor',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdateVendor = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<
    VendorEdit,
    Error,
    { id: string; payload: ManufacturerFormSchema }
  >({
    mutationFn: async ({ id, payload }) =>
      (await updateGeneric({
        controlledListKey: 'supplier',
        id,
        payload,
      })) as unknown as VendorEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'supplier', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Vendor updated successfully',
        variant: 'success',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to update vendor',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteVendor = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<void, Error, string>({
    mutationFn: async (id: string) => {
      await deleteGeneric({ controlledListKey: 'supplier', id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'supplier', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Vendor deleted successfully',
        variant: 'success',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to delete vendor',
        variant: 'destructive',
      });
    },
  });
};
