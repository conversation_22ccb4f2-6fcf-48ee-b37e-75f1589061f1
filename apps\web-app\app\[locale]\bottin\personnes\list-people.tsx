'use client';

import { peopleColumns } from '@/app/[locale]/bottin/personnes/columns';
import { GenericList } from '@/components/bottin-table/generic-list';
import { initialColumnVisibility } from '@/constants/bottin/people';
import type { SupportedLocale } from '@/types/locale';
import type { PersonList } from '@rie/domain/types';

type PeopleListProps = {
  locale: SupportedLocale;
};

export const PeopleList = ({ locale }: PeopleListProps) => {
  return (
    <GenericList<'person', 'list', PersonList, Record<string, unknown>>
      controlledListKey="person"
      view="list"
      locale={locale}
      columns={peopleColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="people"
    />
  );
};
