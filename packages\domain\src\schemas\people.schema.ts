import {
  DbPersonInputSchema,
  DbPersonSelectSchema,
} from '@rie/db-schema/entity-schemas';
import * as Schema from 'effect/Schema';

export const PeopleId = Schema.String.pipe(Schema.brand('PeopleId'));
export type PeopleId = typeof PeopleId.Type;

export const PeopleRawSchema = Schema.Struct({
  id: Schema.NonEmptyString,
  familyName: Schema.NonEmptyString,
  givenName: Schema.NonEmptyString,
  personEmails: Schema.Array(Schema.NonEmptyString),
});

export const PeopleListViewSchema = Schema.Struct({
  id: PeopleId,
  familyName: Schema.NonEmptyString,
  givenName: Schema.NonEmptyString,
  emails: Schema.Array(Schema.NonEmptyString),
});

// — Person List view schema (for directory table)
export const PersonListSchema = Schema.Struct({
  id: Schema.String,
  lastName: Schema.String, // nom de famille
  firstName: Schema.String, // prénom
  email: Schema.Array(Schema.String), // emails
  lastUpdatedAt: Schema.String,
  // Champs internes pour les actions
  guidId: Schema.NullishOr(Schema.String),
  uid: Schema.NullishOr(Schema.String),
  userId: Schema.NullishOr(Schema.String),
});

// — Person Select view schema
export const PersonSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

// — Person Edit view schema
export const PersonEditSchema = Schema.Struct({
  id: Schema.String,
  guidId: Schema.NullishOr(Schema.String),
  uid: Schema.NullishOr(Schema.String),
  firstName: Schema.String,
  lastName: Schema.String,
  userId: Schema.NullishOr(Schema.String),
});

// — Person Detail view schema (same as list for now)
export const PersonDetailSchema = PersonListSchema;

// — Full Person shape (people table doesn't have translations)
export const PersonSchema = Schema.Struct({
  ...DbPersonSelectSchema.omit('modifiedBy').fields,
  emails: Schema.Array(
    Schema.Struct({
      id: Schema.String,
      address: Schema.String,
    }),
  ),
  modifiedBy: Schema.NullishOr(Schema.String),
});

// — Input (create/update) shape
export const PersonInputSchema = Schema.Struct({
  ...DbPersonInputSchema.omit('id').fields,
});

// — Database schema (for serializers)
export const DbPersonSchema = PersonSchema;
