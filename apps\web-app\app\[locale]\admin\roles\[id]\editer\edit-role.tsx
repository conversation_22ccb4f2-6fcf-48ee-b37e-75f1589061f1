'use client';

import { RoleForm } from '@/app/[locale]/admin/roles/(form)/role-form';
import type { RoleFormSchema } from '@/app/[locale]/admin/roles/(form)/role-form.schema';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import {
  useGetRoleById,
  useUpdateRole,
} from '@/hooks/admin/permissions/roles.hook';
import type { DbRole } from '@rie/db-schema/entity-types';

interface EditRoleProps {
  id: string;
}

// For now, transform DbRole to RoleFormSchema with empty arrays
// TODO: Implement proper fetching of role relationships
const transformDbRoleToFormSchema = (role: DbRole): RoleFormSchema => {
  return {
    name: role.name,
    description: role.description || '',
    directPermissions: [], // TODO: Fetch from role's direct permissions
    permissionGroups: [], // At least one permission group is required by schema
    parentRoles: [], // TODO: Fetch from role's parent roles
  };
};

export const EditRole = ({ id }: EditRoleProps) => {
  const { data: role, isLoading, error } = useGetRoleById(id);
  const { mutate: updateRole, status } = useUpdateRole();

  if (isLoading) {
    return <LoadingResource />;
  }

  if (error) {
    return <div className="p-4 text-red-500">Error: {error.message}</div>;
  }

  if (!role) {
    return <div className="p-4 text-red-500">Role not found</div>;
  }

  const handleOnSubmit = (data: RoleFormSchema) => {
    updateRole({ payload: data, id });
  };

  const initialData = transformDbRoleToFormSchema(role);

  return (
    <RoleForm
      initialData={initialData}
      onSubmitAction={handleOnSubmit}
      status={status}
    />
  );
};
