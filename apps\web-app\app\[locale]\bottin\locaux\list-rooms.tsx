'use client';

import { roomsColumns } from '@/app/[locale]/bottin/locaux/columns';
import { GenericList } from '@/components/bottin-table/generic-list';
import { initialColumnVisibility } from '@/constants/bottin/rooms';
import type { SupportedLocale } from '@/types/locale';
import type { RoomList } from '@rie/domain/types';

type RoomsListProps = {
  locale: SupportedLocale;
};

export const RoomsList = ({ locale }: RoomsListProps) => {
  return (
    <GenericList<'local', 'list', RoomList, Record<string, unknown>>
      controlledListKey="local"
      view="list"
      locale={locale}
      columns={roomsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="rooms"
    />
  );
};
