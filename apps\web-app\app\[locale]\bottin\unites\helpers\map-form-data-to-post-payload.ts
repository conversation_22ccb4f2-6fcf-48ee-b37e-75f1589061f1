import { getPostLocalizedValue } from '@/helpers/resources.helpers';
import type { UnitFormSchema } from '@/schemas/bottin/unit-form-schema';
import type { EntityPayload } from '@/types/bottin/directory';
import type { UnitPostPayload } from '@/types/bottin/unit';

export const mapUnitBaseForm = (formData: UnitFormSchema): UnitPostPayload => ({
  acronyms: getPostLocalizedValue(formData.pseudonym),
  names: getPostLocalizedValue(formData.names),
  parent: { id: formData.parentUnit?.value || '' },
  organizationId: Number(formData.relatedOrganizations), //TODO: Verifier si c bien related organizations
  typeUnite: {
    id: formData.unitType,
  },
  pseudonymes: getPostLocalizedValue(formData.pseudonym),
});

export function mapFormDataToPostPayload(
  formData: UnitFormSchema,
): EntityPayload<'unit'> {
  return mapUnitBaseForm(formData);
}
