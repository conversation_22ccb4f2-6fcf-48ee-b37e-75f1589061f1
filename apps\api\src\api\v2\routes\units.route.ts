import { handleEffectError } from '@/api/v2/utils/error-handler';
import { UnitsRuntime } from '@/infrastructure/runtimes/units.runtime';
import { effectValidator } from '@hono/effect-validator';
import {
  CollectionViewParamSchema,
  CollectionViewSchema,
  ResourceIdSchema,
  ResourceQuerySchema,
  ResourceViewSchema,
  UnitInputSchema,
  UnitListSchema,
  UnitSchema,
} from '@rie/domain/schemas';
import { dbUnitsToUnits } from '@rie/domain/serializers';
import type { HonoVariables } from '@rie/domain/types';
import { UnitsServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// OpenAPI route descriptions
export const getAllUnitsRoute = describeRoute({
  description: 'Lister toutes les unités',
  operationId: 'getAllUnits',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(CollectionViewSchema),
      description: 'Type de vue (list, select)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(UnitListSchema)),
        },
      },
      description: 'Unités retournées',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const getUnitByIdRoute = describeRoute({
  description: 'Obtenir une unité par ID',
  operationId: 'getUnitById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'unité (format CUID)",
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(ResourceViewSchema),
      description: 'Type de vue (detail, edit)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(UnitSchema),
        },
      },
      description: 'Unité trouvée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Unité non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const createUnitRoute = describeRoute({
  description: 'Créer une unité',
  operationId: 'createUnit',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(UnitInputSchema),
        example: {
          guidId: 'unit_guid_123',
          typeId: 'unit_type_123',
          parentId: 'parent_unit_123',
          translations: [
            {
              locale: 'fr',
              name: 'Département Informatique',
              description: 'Description du département',
              otherNames: null,
              acronyms: 'DI',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(UnitSchema),
        },
      },
      description: 'Unité créée',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description:
        "Erreur de validation - Données d'entrée invalides ou champs requis manquants",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const updateUnitRoute = describeRoute({
  description: 'Mettre à jour une unité',
  operationId: 'updateUnit',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'unité (format CUID)",
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(UnitInputSchema),
        example: {
          guidId: 'updated_unit_guid_123',
          typeId: 'updated_unit_type_123',
          parentId: 'updated_parent_unit_123',
          translations: [
            {
              locale: 'fr',
              name: 'Département Informatique Mise à Jour',
              description: 'Description mise à jour',
              otherNames: null,
              acronyms: 'DI-MAJ',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(UnitSchema),
        },
      },
      description: 'Unité mise à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Unité non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

export const deleteUnitRoute = describeRoute({
  description: 'Supprimer une unité',
  operationId: 'deleteUnit',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'unité (format CUID)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Unité supprimée',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Unité non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Units'],
});

const unitsRoute = new Hono<{
  Variables: HonoVariables;
}>();

unitsRoute.get(
  '/',
  getAllUnitsRoute,
  effectValidator('query', CollectionViewParamSchema),
  async (ctx) => {
    const { view = 'list' } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      const units = yield* unitService.getAllUnits();
      return dbUnitsToUnits(units, view);
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.get(
  '/:id',
  getUnitByIdRoute,
  effectValidator('query', ResourceQuerySchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const { view } = ctx.req.valid('query');
    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.getUnitById({
        id,
        view,
      });
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.post(
  '/',
  createUnitRoute,
  effectValidator('json', UnitInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.createUnit({
        ...body,
        modifiedBy: user?.id,
      });
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.put(
  '/:id',
  updateUnitRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('json', UnitInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.updateUnit({
        id,
        unit: {
          ...body,
          modifiedBy: user?.id,
        },
      });
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

unitsRoute.delete(
  '/:id',
  deleteUnitRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const unitService = yield* UnitsServiceLive;
      return yield* unitService.deleteUnit(id);
    });
    const result = await UnitsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Unit deleted' });
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { unitsRoute };
