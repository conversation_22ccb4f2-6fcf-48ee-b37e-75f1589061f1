import { handleEffectError } from '@/api/v2/utils/error-handler';
import { PermissionGroupsRuntime } from '@/infrastructure/runtimes/permission-groups.runtime';
import { effectValidator } from '@hono/effect-validator';
import {
  CollectionViewParamSchema,
  PermissionsGroupDetailSchema,
  PermissionsGroupInputSchema,
  PermissionsGroupListSchema,
  ResourceIdSchema,
  ResourceQuerySchema,
  ResourceViewSchema,
} from '@rie/domain/schemas';
import { PermissionsGroupsServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// Route descriptions for OpenAPI
const getAllPermissionGroupsRoute = describeRoute({
  description: 'Obtient tous les groupes de permissions',
  operationId: 'getAllPermissionGroups',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue pour la collection (list ou select)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(PermissionsGroupListSchema)),
        },
      },
      description: 'Groupes de permissions retournés avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permission Groups'],
});

const getPermissionGroupByIdRoute = describeRoute({
  description: 'Obtient un groupe de permissions par son ID',
  operationId: 'getPermissionGroupById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du groupe de permissions',
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(ResourceViewSchema),
      description:
        'Type de vue: detail (avec locale requis) ou edit (locale optionnel)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(PermissionsGroupDetailSchema),
        },
      },
      description: 'Groupe de permissions retourné avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Groupe de permissions non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permission Groups'],
});

const createPermissionGroupRoute = describeRoute({
  description: 'Crée un groupe de permissions',
  operationId: 'createPermissionGroup',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(PermissionsGroupInputSchema),
        example: {
          name: 'Equipment Management',
          description: 'Permissions for managing equipment',
          permissions: [
            { value: 'equipment_read_01', label: 'equipment - read' },
            { value: 'equipment_update_01', label: 'equipment - update' },
            { value: 'equipment_create_01', label: 'equipment - create' },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(PermissionsGroupInputSchema),
        },
      },
      description: 'Groupe de permissions créé avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Groupe de permissions déjà existant',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permission Groups'],
});

const updatePermissionGroupRoute = describeRoute({
  description: 'Mettre à jour un groupe de permissions',
  operationId: 'updatePermissionGroup',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du groupe de permissions à mettre à jour',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(PermissionsGroupInputSchema),
        example: {
          name: 'Infrastructure Management',
          description: 'Updated permissions for managing infrastructure',
          permissions: [
            { value: 'infrastructure_read_01', label: 'infrastructure - read' },
            {
              value: 'infrastructure_update_01',
              label: 'infrastructure - update',
            },
            {
              value: 'infrastructure_delete_01',
              label: 'infrastructure - delete',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(PermissionsGroupInputSchema),
        },
      },
      description: 'Groupe de permissions mis à jour avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Groupe de permissions non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permission Groups'],
});

const deletePermissionGroupRoute = describeRoute({
  description: 'Supprime un groupe de permissions',
  operationId: 'deletePermissionGroup',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du groupe de permissions à supprimer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              success: Schema.Boolean,
              message: Schema.String,
            }),
          ),
        },
      },
      description: 'Groupe de permissions supprimé avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Groupe de permissions non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Permission Groups'],
});
// Create the router
const permissionsGroupsRoute = new Hono();

// Get all permission groups
permissionsGroupsRoute.get(
  '/',
  getAllPermissionGroupsRoute,
  effectValidator('query', CollectionViewParamSchema),
  async (ctx) => {
    const { view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const permissionGroupsService = yield* PermissionsGroupsServiceLive;
      return yield* permissionGroupsService.getAllPermissionsGroups({ view });
    });

    const permissionGroups =
      await PermissionGroupsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, permissionGroups);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(permissionGroups)) {
      return ctx.json(permissionGroups.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Get permission group by ID
permissionsGroupsRoute.get(
  '/:id',
  getPermissionGroupByIdRoute,
  effectValidator('query', ResourceQuerySchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const { view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const permissionGroupsService = yield* PermissionsGroupsServiceLive;
      return yield* permissionGroupsService.getPermissionsGroupById({
        id,
        view,
      });
    });

    const permissionGroup =
      await PermissionGroupsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, permissionGroup);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(permissionGroup)) {
      return ctx.json(permissionGroup.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Create a new permission group
permissionsGroupsRoute.post(
  '/',
  createPermissionGroupRoute,
  effectValidator('json', PermissionsGroupInputSchema),
  async (ctx) => {
    const { name, description, permissions } = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const permissionGroupsService = yield* PermissionsGroupsServiceLive;
      return yield* permissionGroupsService.createPermissionsGroup({
        name,
        description,
        permissions,
      });
    });

    const permissionsGroup =
      await PermissionGroupsRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, permissionsGroup);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(permissionsGroup)) {
      return ctx.json(permissionsGroup.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Update a permission group
permissionsGroupsRoute.put(
  '/:id',
  updatePermissionGroupRoute,
  effectValidator('json', PermissionsGroupInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const { name, description, permissions } = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const permissionGroupsService = yield* PermissionsGroupsServiceLive;
      return yield* permissionGroupsService.updatePermissionsGroup({
        id,
        name,
        description,
        permissions,
      });
    });

    const permissionGroup =
      await PermissionGroupsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, permissionGroup);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(permissionGroup)) {
      return ctx.json(permissionGroup.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Delete a permission group
permissionsGroupsRoute.delete(
  '/:id',
  deletePermissionGroupRoute,
  async (ctx) => {
    const id = ctx.req.param('id');

    const program = Effect.gen(function* () {
      const permissionGroupsService = yield* PermissionsGroupsServiceLive;
      return yield* permissionGroupsService.deletePermissionsGroup(id);
    });

    const result = await PermissionGroupsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({
        success: result.value,
        message: 'Permission group deleted successfully',
      });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export default permissionsGroupsRoute;
