import {
  AddResource,
  type AddResourceProps,
} from '@/components/add-resource/add-resource';
import BackButton from '@/components/back-button/back-button';
import { EditButton } from '@/components/edit-button/edit-button';
import { PermissionGate } from '@/components/permissions/permission-gate';
import type { AppEditPathnames } from '@/i18n/settings';
import type { ResourceType } from '@rie/domain/types';
import { useTranslations } from 'next-intl';

type DetailsHeaderButtonsProps = {
  addEquipmentHref?: AddResourceProps['addResourceHref'];
  id: string;
  pathname: AppEditPathnames;
  resource: ResourceType;
};

const DetailsHeaderButtons = ({
  addEquipmentHref,
  id,
  pathname,
  resource,
}: DetailsHeaderButtonsProps) => {
  const t = useTranslations('common');

  return (
    <div className="mb-4 flex justify-between">
      <BackButton />
      {addEquipmentHref && resource === 'infrastructure' && (
        <div className="ml-auto mr-5">
          <PermissionGate action="create" resourceType="equipment">
            <AddResource
              addResourceHref={addEquipmentHref}
              addResourceLabel={t('addEquipment')}
            />
          </PermissionGate>
        </div>
      )}
      <PermissionGate resourceId={id} action="update" resourceType={resource}>
        <EditButton id={id} pathname={pathname} resource={resource} />
      </PermissionGate>
    </div>
  );
};

export default DetailsHeaderButtons;
