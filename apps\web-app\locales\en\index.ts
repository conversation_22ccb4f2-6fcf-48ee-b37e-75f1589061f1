// Import all locale messages
import buildings from './buildings.locale.json';
import common from './common.locale.json';
import contractService from './contract-service.locale.json';
import controlledLists from './controlledLists.locale.json';
import establishments from './directory.locale.json';
import equipments from './equipments.locale.json';
import error from './error.locale.json';
import facets from './facets.locale.json';
import financingProject from './financing-project.locale.json';
import forms from './forms.locale.json';
import homePage from './home-page.locale.json';
import infrastructures from './infrastructures.locale.json';
import navigation from './navigation.locale.json';
import permissions from './permission.locale.json';
import permissionsGroups from './permissions-groups.locale.json';
import profile from './profile.locale.json';
import roles from './roles.locale.json';
import rooms from './rooms.locale.json';
import units from './units.locale.json';
import user from './user.locale.json';

const directory = establishments;
const manufacturers = directory;
const people = directory;

// Export individual message objects
export const messages = {
  buildings,
  common,
  contractService,
  controlledLists,
  establishments,
  directory,
  equipments,
  error,
  facets,
  financingProject,
  forms,
  homePage,
  infrastructures,
  manufacturers,
  navigation,
  people,
  permissions,
  permissionsGroups,
  profile,
  roles,
  rooms,
  units,
  user,
} as const;

export type Namespace = keyof typeof messages;
