import { PgDatabaseLayer } from '@rie/postgres-db';
import { RoomsRepositoryLive } from '@rie/repositories';
import { RoomsServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const RoomsServicesLayer = Layer.mergeAll(
  RoomsRepositoryLive.Default,
  RoomsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const RoomsRuntime = ManagedRuntime.make(RoomsServicesLayer);
