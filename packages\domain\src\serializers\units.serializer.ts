import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  UnitEditSchema,
  UnitFormSchema,
  UnitInputSchema,
  UnitListSchema,
  UnitSchema,
  UnitSelectSchema,
} from '../schemas';
import type {
  CollectionViewType,
  ResourceViewType,
  Unit,
  UnitList,
  UnitSelect,
} from '../types';

// Schema transformer for converting database unit to list view
export const DbUnitToUnitList = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.String,
    isActive: Schema.NullishOr(Schema.Boolean),
    guidId: Schema.NullishOr(Schema.String),
    typeId: Schema.NullishOr(Schema.String),
    parentId: Schema.NullishOr(Schema.String),
    createdAt: Schema.String,
    updatedAt: Schema.String,
    modifiedBy: Schema.NullishOr(Schema.String),
    translations: Schema.Array(
      Schema.Struct({
        id: Schema.String,
        locale: Schema.String,
        name: Schema.NullishOr(Schema.String),
        description: Schema.NullishOr(Schema.String),
        otherNames: Schema.NullishOr(Schema.String),
        acronyms: Schema.NullishOr(Schema.String),
      }),
    ),
    parent: Schema.NullishOr(
      Schema.Struct({
        id: Schema.String,
        type: Schema.Literal('institution', 'unit'),
        institution: Schema.NullishOr(
          Schema.Struct({
            id: Schema.String,
            translations: Schema.Array(
              Schema.Struct({
                locale: Schema.String,
                name: Schema.NullishOr(Schema.String),
              }),
            ),
          }),
        ),
        unit: Schema.NullishOr(
          Schema.Struct({
            id: Schema.String,
            translations: Schema.Array(
              Schema.Struct({
                locale: Schema.String,
                name: Schema.NullishOr(Schema.String),
              }),
            ),
          }),
        ),
      }),
    ),
  }),
  UnitListSchema,
  {
    strict: false, // Allow missing fields
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation (could be 'fr' or 'en')
          const defaultTranslation =
            raw.translations?.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations?.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations?.[0];

          // Get parent name from the parent relation
          let parentName: string | null = null;
          if (raw.parent) {
            if (raw.parent.type === 'institution' && raw.parent.institution) {
              const parentTranslation =
                raw.parent.institution.translations?.find(
                  (t) => t.locale === 'fr',
                ) ||
                raw.parent.institution.translations?.find(
                  (t) => t.locale === 'en',
                ) ||
                raw.parent.institution.translations?.[0];
              parentName = parentTranslation?.name || null;
            } else if (raw.parent.type === 'unit' && raw.parent.unit) {
              const parentTranslation =
                raw.parent.unit.translations?.find((t) => t.locale === 'fr') ||
                raw.parent.unit.translations?.find((t) => t.locale === 'en') ||
                raw.parent.unit.translations?.[0];
              parentName = parentTranslation?.name || null;
            }
          }

          return {
            id: raw.id,
            text: defaultTranslation?.name || raw.id,
            acronym: defaultTranslation?.acronyms || null,
            parentName,
            lastUpdatedAt: raw.updatedAt,
            guidId: raw.guidId || null,
            typeId: raw.typeId || null,
            parentId: raw.parentId || null,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse unit for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database unit to select view
export const DbUnitToUnitSelect = Schema.transformOrFail(
  Schema.Struct({
    id: Schema.String,
    isActive: Schema.NullishOr(Schema.Boolean),
    guidId: Schema.NullishOr(Schema.String),
    typeId: Schema.NullishOr(Schema.String),
    parentId: Schema.NullishOr(Schema.String),
    createdAt: Schema.String,
    updatedAt: Schema.String,
    modifiedBy: Schema.NullishOr(Schema.String),
    translations: Schema.Array(
      Schema.Struct({
        id: Schema.String,
        locale: Schema.String,
        name: Schema.NullishOr(Schema.String),
        description: Schema.NullishOr(Schema.String),
        otherNames: Schema.NullishOr(Schema.String),
        acronyms: Schema.NullishOr(Schema.String),
      }),
    ),
  }),
  UnitSelectSchema,
  {
    strict: false, // Allow missing fields
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation for label
          const defaultTranslation =
            raw.translations?.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations?.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations?.[0];

          return {
            value: raw.id,
            label: defaultTranslation?.name || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse unit for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database unit to edit format
export const DbUnitToUnitEdit = Schema.transformOrFail(
  UnitSchema,
  UnitEditSchema,
  {
    strict: false, // Allow missing fields
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            guidId: raw.guidId ?? undefined,
            typeId: raw.typeId ?? undefined,
            parentId: raw.parentId ?? undefined,
            translations: raw.translations.map((translation) => ({
              locale: translation.locale as 'fr' | 'en',
              name: translation.name || undefined,
              description: translation.description || undefined,
              otherNames: translation.otherNames || undefined,
              acronyms: translation.acronyms || undefined,
            })),
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse unit for edit format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Helper function to transform an array of Unit to UnitSelect[]
export const dbUnitsToUnitSelect = (dbUnits: Unit[]): UnitSelect[] => {
  return dbUnits.map((dbUnit) => {
    const defaultTranslation =
      dbUnit.translations?.find((translation) => translation.locale === 'fr') ||
      dbUnit.translations?.find((translation) => translation.locale === 'en') ||
      dbUnit.translations?.[0];

    return {
      value: dbUnit.id,
      label: defaultTranslation?.name || dbUnit.id,
    };
  });
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbUnitsToUnits = (
  dbUnits: Array<{
    id: string;
    isActive: boolean | null;
    guidId: string | null;
    typeId: string | null;
    parentId: string | null;
    createdAt: string;
    updatedAt: string;
    modifiedBy: string | null;
    translations: Array<{
      id: string;
      locale: string;
      name: string | null;
      description: string | null;
      otherNames: string | null;
      acronyms: string | null;
    }>;
    parent?: {
      id: string;
      type: 'institution' | 'unit';
      institution?: {
        id: string;
        translations: Array<{
          locale: string;
          name: string | null;
        }>;
      } | null;
      unit?: {
        id: string;
        translations: Array<{
          locale: string;
          name: string | null;
        }>;
      } | null;
    } | null;
  }>,
  view: CollectionViewType,
): UnitList[] | UnitSelect[] => {
  return view === 'select'
    ? dbUnits.map((unit) => Schema.decodeUnknownSync(DbUnitToUnitSelect)(unit))
    : dbUnits.map((unit) => Schema.decodeUnknownSync(DbUnitToUnitList)(unit));
};

// New serializer function for Unit with view parameter
export const dbUnitToUnit = (dbUnit: unknown, view: ResourceViewType) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbUnitToUnitEdit)(dbUnit)
    : Schema.decodeUnknownSync(DbUnitToUnitList)(dbUnit);
};

// Transform client UnitFormSchema payload into UnitInputSchema (DB input)
export const UnitFormToDBInput = Schema.transformOrFail(
  UnitFormSchema,
  UnitInputSchema,
  {
    strict: false,
    decode: (val, _options, ast) =>
      ParseResult.try({
        try: () => {
          const translations = val.names.map(({ locale, value }) => {
            const alias = val.alias.find((a) => a.locale === locale)?.value;
            const acronym = val.pseudonym.find(
              (p) => p.locale === locale,
            )?.value;
            return {
              locale,
              name: value || null,
              description: undefined,
              otherNames: alias ?? undefined,
              acronyms: acronym ?? undefined,
            };
          });
          return {
            guidId: null,
            typeId: val.unitType,
            parentId: val.parentUnit.value ?? null,
            translations,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            val,
            error instanceof Error
              ? error.message
              : 'Failed to convert unit form to DB input',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);
