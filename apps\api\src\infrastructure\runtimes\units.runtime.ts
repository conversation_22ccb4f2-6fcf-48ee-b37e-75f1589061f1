import { PgDatabaseLayer } from '@rie/postgres-db';
import {
  InfrastructuresRepositoryLive,
  UnitsRepositoryLive,
  UsersRepositoryLive,
} from '@rie/repositories';
import {
  AccessTreeServiceLive,
  UnitsServiceLive,
  UserPermissionsServiceLive,
} from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const UnitsServicesLayer = Layer.mergeAll(
  UnitsRepositoryLive.Default,
  UnitsServiceLive.Default,
  // Policy dependencies
  UsersRepositoryLive.Default,
  InfrastructuresRepositoryLive.Default,
  UserPermissionsServiceLive.Default,
  AccessTreeServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const UnitsRuntime = ManagedRuntime.make(UnitsServicesLayer);
