import type { PermissionFormSchema } from '@/app/[locale]/admin/permissions/(forms)/permission-form.schema';
import { useToast } from '@/components/hooks/use-toast';
import {
  getAllPermissionsOptions,
  getPermissionByIdOptions,
} from '@/hooks/admin/permissions/permissions.options';
import { useRouter } from '@/lib/navigation';
import {
  createPermission,
  deletePermission,
  updatePermission,
} from '@/services/permissions/permissions.service';
import type { PermissionResultType } from '@/types/permission.type';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

export const useGetAllPermissions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) => {
  return useQuery<PermissionResultType<View>>(
    getAllPermissionsOptions({ view }),
  );
};

interface GetPermissionByIdParams {
  id: string;
  view: ResourceViewType;
}

export const useGetPermissionById = <View extends ResourceViewType>({
  id,
  view,
}: GetPermissionByIdParams) => {
  return useQuery(getPermissionByIdOptions<View>({ id, view }));
};

export const useCreatePermission = () => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (payload: PermissionFormSchema) => {
      return await createPermission(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
      toast({
        title: 'Success',
        description: 'Permission created successfully',
        variant: 'success',
      });
      router.push('/admin/permissions');
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to create permission',
        variant: 'destructive',
      });
    },
  });
};

export const useUpdatePermission = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      payload,
      id,
    }: { payload: PermissionFormSchema; id: string }) =>
      updatePermission({ id, payload }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to update permission',
        variant: 'destructive',
      });
    },
  });
};

export const useDeletePermission = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => deletePermission(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to delete permission',
        variant: 'destructive',
      });
    },
  });
};
