import { PermissionGate } from '@/components/permissions/permission-gate';
import { ResourceGrid } from '@/components/resource-grid/resource-grid';
import { ScrollToTopButton } from '@/components/scroll-to-top-button/scroll-to-top-button';
import type { DocumentType } from '@/types/equipment';
import { useTranslations } from 'next-intl';

import { DocumentCard } from './document-card';

type EquipmentDocumentsProps = {
  documents: DocumentType[];
};

export const Documents = ({ documents }: EquipmentDocumentsProps) => {
  const t = useTranslations('equipments.details.sections.documents');

  if (documents.length === 0) {
    return (
      <div className="flex h-40 items-center justify-center">
        <p> {t('fields.countDocuments', { count: 0 })} </p>
      </div>
    );
  }
  return (
    <div className="relative mt-3">
      <ResourceGrid>
        {documents.map((document) =>
          document.isConfidential ? (
            <PermissionGate
              key={document.id}
              action="read"
              // TODO: Check about this permission with Sakinah
              resourceType="equipment"
            >
              <DocumentCard
                className="h-full"
                key={document.id}
                {...document}
              />
            </PermissionGate>
          ) : (
            <DocumentCard className="h-full" key={document.id} {...document} />
          ),
        )}
      </ResourceGrid>
      <ScrollToTopButton />
    </div>
  );
};
