import type { equipmentFormSections } from '@/constants/equipments';
import type {
  infrastructureFormSections,
  infrastructureStatus,
  resourceStatus,
} from '@/constants/infrastructures';
import type { IdType } from '@/types/bottin/project';

import type { BuildingFull, CampusFull } from '@/types/building';
import type {
  AddressPayload,
  EntityLocaleDictionary,
  EntityLocation,
  Manager,
  Visibility,
} from '@/types/common';
import type { PersonFull, UnitFull } from '@/types/controlled-list';

type InfrastructureType = {
  descriptions?: EntityLocaleDictionary;
  id: number;
  nom: string;
  noms: EntityLocaleDictionary;
  slug: string;
  text: string;
  uid: string;
};

type Establishment = {
  acronym: null | string;
  acronyms: EntityLocaleDictionary;
  createdAt: string;
  dateBegin: null | string;
  dateEnd: null | string;
  descriptions?: EntityLocaleDictionary;
  id: string;
  lastUpdatedAt: null | string;
  names: EntityLocaleDictionary;
  organizationId: number;
  parent: Establishment | null;
  pseudonymes: EntityLocaleDictionary;
  text: string;
  typeContenu?: string;
  typeEtablissement?: InfrastructureType;
  uid: string;
};

type InfrastructureStatusStructure = {
  descriptions: EntityLocaleDictionary;
  id: number;
  nom: string;
  noms: EntityLocaleDictionary;
  slug: string;
  text: string;
  uid: string;
};
type InnovationLab = {
  descriptions: EntityLocaleDictionary;
  id: number;
  nom: string;
  noms: EntityLocaleDictionary;
  slug: null | string;
  text: string;
  uid: null | string;
};
export type Affiliations = {
  fonction: string;
  id: number;
  person: PersonFull;
  text: string;
  typeFonction: {
    id: number;
    text: string;
  };
};
export type InfrastructureFull = {
  acronyme?: null | string;
  acronymes?: EntityLocaleDictionary;
  batimentLocaux: BuildingFull[];
  campusLocaux: CampusFull[];
  createdAt: string;
  descriptions: EntityLocaleDictionary;
  directeursTechnique: Manager[];
  emplacement: EntityLocation | null;
  id: string;
  ID?: string;
  infrastructureAffiliations?: Affiliations[];
  infrastructureId: number;
  infrastructureReception: EntityLocation | null;
  juridiction: Establishment;
  laboratoireInnovations: InnovationLab[] | null;
  lastUpdatedAt: string;
  locaux: Local[];
  mettreEnVedette: boolean;
  nom: string;
  noms: EntityLocaleDictionary;
  projetFinancements: FinancialProject[];
  pseudonyme?: null | string;
  responsablesSST: Manager[];
  responsablesTechnique: Manager[];
  statutInfrastructure: InfrastructureStatusStructure;
  telephone: null | string;
  text: string;
  totalEquipements: number;
  typeContenu: string;
  typeInfrastructure: InfrastructureType;
  uid: null | string;
  unite: UnitFull;
  url?: null | string;
  visibilite: Visibility;
};
export type FinancialProject = {
  id: number;
  nom: string;
  noms: EntityLocaleDictionary;
  slug: null | string;
  text: string;
  uid: null | string;
};

export type Local = {
  batiment: {
    campus: {
      etablissement: Establishment;
      id: number;
      nom: string;
      noms: EntityLocaleDictionary;
      pseudonyme: null | string;
      slug: string;
      text: string;
      uid: string;
    };
    id: number;
    juridiction: Establishment;
    nom: string;
    noms: EntityLocaleDictionary;
    pseudonyme: null | string;
    slug: null | string;
    text: string;
    uid: string;
  };
  id: number;
  juridiction: {
    acronym: null | string;
    acronyms: Array<{ [key: string]: null | string }>;
    createdAt: string;
    dateBegin: null | string;
    dateEnd: null | string;
    descriptions: EntityLocaleDictionary;
    id: string;
    lastUpdatedAt: null | string;
    names: EntityLocaleDictionary;
    organizationId: number;
    parent: Establishment | null;
    pseudonymes: Array<{ [key: string]: null | string }>;
    text: string;
    typeContenu: string;
    uid: string;
  };
  numero: string;
  pseudonyme: null | string;
  slug: null | string;
  text: string;
};

export type Infrastructure = {
  address: string;
  equipmentCount: number;
  id: string;
  jurisdiction: string;
  location?: EntityLocation;
  name: string;
  parentUnit?: string;
  scientificDirector: string;
  status: ResourceStatusType;
  statusText: string;
  technicalDirector?: null | string;
  type: string;
  updatedAt: string;
};

export type InfrastructureDescription = {
  address?: string;
  directeursTechnique?: string | string[];
  emplacement?: EntityLocation | null;
  nom?: string;
  pseudonyme?: null | string;
  responsablesTechnique?: string | string[];
  status: ResourceStatusType;
  statusText?: null | string;
  telephone?: null | string;
  url?: null | string;
  // type: string;
};

export type InfrastructureDetails = {
  affiliated: string[];
  descriptions?: EntityLocaleDictionary;
  emplacement?: string;
  jurisdiction?: string;
  parentUnit?: string;
  projects: string;
  researcher: string[];
  responsablesSST?: null | string[];
};

export type Territory = {
  city: string;
  countryCode: null | string;
  latitude: string;
  longitude: string;
  placeId: string;
  postalCode: string;
  provider: string;
  state: null | string;
  street: string;
  streetNumber: string;
};

export type InfrastructurePostPayload = {
  acronymes?: EntityLocaleDictionary;
  descriptions?: EntityLocaleDictionary;
  directeursTechnique?: IdType[];
  infrastructureAffiliations: {
    fonction: string;
    person: IdType;
    typeFonction: IdType;
  }[];
  infrastructureId?: number;
  infrastructureReception?: AddressPayload | null;
  juridiction: IdType;
  laboratoireInnovations?: IdType[];
  locaux?: IdType[];
  mettreEnVedette: boolean;
  noms: EntityLocaleDictionary;
  projetsFinancements?: IdType[];
  pseudonyme?: null | string;
  responsablesSST?: IdType[];
  responsablesTechnique?: IdType[];
  statutInfrastructure: IdType;
  telephone?: null | string;
  typeInfrastructure: IdType;
  unite: IdType;
  url?: null | string;
  visibilite: IdType;
};

export type InfrastructurePutFormSchema = InfrastructurePostPayload;

export type InfrastructureFormSectionKey =
  keyof typeof infrastructureFormSections;

export type InfrastructureStatus = keyof typeof infrastructureStatus;

export type EquipmentFormSectionKey = keyof typeof equipmentFormSections;

export type ResourceStatusType = keyof typeof resourceStatus;
type ResourceStatus = (typeof resourceStatus)[ResourceStatusType];
export type { ResourceStatus };
