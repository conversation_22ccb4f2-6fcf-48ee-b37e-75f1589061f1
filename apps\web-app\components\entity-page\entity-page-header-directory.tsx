'use client';

import {
  AddResource,
  type AddResourceProps,
} from '@/components/add-resource/add-resource';
import { Filters } from '@/components/filters/filters';
import { SelectedFilters } from '@/components/filters/selected-filters';
import {
  HeaderResource,
  type HeaderResourceProps,
} from '@/components/header-resource/header-resource';
import { PermissionGate } from '@/components/permissions/permission-gate';
import { SearchInput } from '@/components/search-input/search-input';
import { userRoles } from '@/constants/users';
import type { DirectoryEntity } from '@rie/domain/types';
import { useAppStore } from '@/providers/app-store-provider';
import type { FacetValue } from '@/types/filters';
import { Label } from '@/ui/label';
import { Switch } from '@/ui/switch';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { Sort } from '../sort/sort';

type EntityPageDirectoryHeaderProps = {
  facets?: Record<string, FacetValue[]>; //FiltersProps
  resource: DirectoryEntity;
  searchPlaceholder?: string;
} & AddResourceProps &
  // FiltersProps &
  HeaderResourceProps;

export const EntityPageDirectoryHeader = ({
  addResourceHref,
  addResourceLabel,
  countLabel,
  facets,
  onToggle,
  resource,
  searchPlaceholder,
  viewMode,
}: EntityPageDirectoryHeaderProps) => {
  const tCommon = useTranslations('common');
  const user = useAppStore((state) => state.user);
  const [myResourcesSwitch, setMyResourcesSwitch] = useState(false);
  const toggleViewOwnResources = useAppStore(
    (store) => store.toggleViewOwnResources,
  );

  const handleToggleMyResources = (checked: boolean) => {
    setMyResourcesSwitch(checked);
    toggleViewOwnResources(checked);
  };

  return (
    <div>
      <div className="mb-3 flex w-full flex-wrap gap-4">
        <div className="flex min-w-[300px] flex-1 items-center gap-4">
          <Filters facets={facets ?? {}} />
          <SearchInput placeholder={searchPlaceholder} />
        </div>

        <div className="flex flex-wrap items-center gap-4">
          <Sort />

          {user?.roles.has(userRoles.infrastructureManager) ? (
            <div className="flex items-center gap-3">
              <Switch
                checked={myResourcesSwitch}
                id="own-resources"
                onCheckedChange={handleToggleMyResources}
              />
              <Label
                className="whitespace-nowrap hover:cursor-pointer"
                htmlFor="own-resources"
              >
                {tCommon('viewOwnResources', {
                  resources: tCommon(resource).toLowerCase(),
                })}
              </Label>
            </div>
          ) : null}

          <PermissionGate action="create" resourceType={resource}>
            <AddResource
              addResourceHref={addResourceHref}
              addResourceLabel={addResourceLabel}
            />
          </PermissionGate>
        </div>
      </div>
      <SelectedFilters facets={facets ?? {}} />
      <HeaderResource
        countLabel={countLabel}
        onToggle={onToggle}
        viewMode={viewMode}
      />
    </div>
  );
};
