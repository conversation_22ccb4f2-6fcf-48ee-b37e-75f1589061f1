import type { AppPathnames } from '@/i18n/settings';
import { Link } from '@/lib/navigation';
import { cn } from '@/lib/utils';

type DetailPagePathnames = Extract<AppPathnames, `${string}/[id]`>;

type EntityTitleLinkProps = {
  id: string;
  title: string;
  pathname: DetailPagePathnames;
  className?: string;
};

export const EntityTitleLink = ({
  id,
  title,
  pathname,
  className,
}: EntityTitleLinkProps) => {
  return (
    <Link
      className={cn(
        'text-blue-600 hover:text-blue-800 hover:underline',
        className,
      )}
      href={{
        params: { id },
        pathname,
      }}
    >
      {title}
    </Link>
  );
};
