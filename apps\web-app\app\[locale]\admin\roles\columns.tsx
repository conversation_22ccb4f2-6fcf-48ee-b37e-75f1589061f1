'use client';

import { <PERSON>er<PERSON>enderer } from '@/components/header-renderer/header-renderer';
import { TableActionButtons } from '@/components/table-action-buttons/table-action-buttons';
import { Badge } from '@/components/ui/badge';
import dayjs from '@/lib/dayjs';
import type { SupportedLocale } from '@/types/locale';
import type { RoleList } from '@rie/domain/types';
import type { ColumnDef } from '@tanstack/react-table';

const namespace = 'roles';
export const roleColumns = (locale: SupportedLocale): ColumnDef<RoleList>[] => {
  return [
    {
      accessorKey: 'name',
      cell: (cell) => cell.getValue(),
      enableHiding: false,
      enablePinning: true,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'name',
      size: 280,
    },
    {
      accessorKey: 'description',
      cell: (cell) => cell.getValue(),
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'description',
      size: 280,
    },
    {
      accessorKey: 'directPermissions',
      cell: ({ row }) => {
        const value = row.original.directPermissions;

        return value.map((permission) => (
          <Badge
            key={permission.id}
          >{`${permission.domain}.${permission.action}`}</Badge>
        ));
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'directPermissions',
      size: 280,
    },
    {
      accessorKey: 'permissionsGroups',
      cell: ({ row }) => {
        const value = row.original.permissionsGroups;
        return (
          <div className="flex flex-wrap gap-2">
            {value.map((group) => (
              <Badge key={group.id}>{group.name}</Badge>
            ))}
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'permissionsGroups',
      size: 280,
    },
    {
      accessorKey: 'parentRoles',
      cell: ({ row }) => {
        const value = row.original.parentRoles;

        return (
          <div className="flex flex-wrap gap-2">
            {value.map((parent) => (
              <Badge key={parent.id}>{parent.name}</Badge>
            ))}
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => {
        return (
          <HeaderRenderer
            namespace={namespace}
            translationKey={`table.columns.${header.id}`}
          />
        );
      },
      id: 'parentRoles',
      size: 280,
    },
    {
      accessorKey: 'createdAt',
      cell: (cell) =>
        dayjs(cell.getValue() as string)
          .locale(locale)
          .format('L'),
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      id: 'createdAt',
      maxSize: 100,
    },
    {
      accessorKey: 'updatedAt',
      cell: (cell) =>
        dayjs(cell.getValue() as string)
          .locale(locale)
          .format('L'),
      enableHiding: true,
      enablePinning: false,
      header: ({ header }) => (
        <HeaderRenderer
          namespace={namespace}
          translationKey={`table.columns.${header.id}`}
        />
      ),
      id: 'updatedAt',
      maxSize: 100,
    },
    {
      accessorKey: 'roleActions',
      cell: ({ row }) => (
        <TableActionButtons
          id={row.original.id}
          name={row.original.name}
          pathname="/admin/roles/[id]/editer"
        />
      ),
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={namespace}
          translationKey="table.columns.actions"
        />
      ),
      id: 'roleActions',
      size: 100,
    },
  ];
};
