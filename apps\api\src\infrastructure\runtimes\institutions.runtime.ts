import { PgDatabaseLayer } from '@rie/postgres-db';
import { InstitutionsRepositoryLive } from '@rie/repositories';
import { InstitutionsServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const InstitutionsServicesLayer = Layer.mergeAll(
  InstitutionsRepositoryLive.Default,
  InstitutionsServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const InstitutionsRuntime = ManagedRuntime.make(
  InstitutionsServicesLayer,
);
