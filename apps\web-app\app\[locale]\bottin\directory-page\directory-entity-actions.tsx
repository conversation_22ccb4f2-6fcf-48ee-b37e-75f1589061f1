import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { useDeleteDirectoryEntity } from '@/hooks/bottin/use-delete-directory-entity';
import { extractDirectoryEditPathnames } from '@/i18n/i18n.helpers';
import { pathnames } from '@/i18n/settings';
import type { DirectoryEntity } from '@rie/domain/types';
import { Link } from '@/lib/navigation';
import type { Entity } from '@/types/bottin/directory';
import { Button } from '@/ui/button';
import type { Row } from '@tanstack/react-table';
import { useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

type DirectoryEntityActionsProps = {
  row: Row<Entity>;
  directoryEntity: DirectoryEntity;
};
export const DirectoryEntityActions = ({
  row,
  directoryEntity,
}: DirectoryEntityActionsProps) => {
  const tCommon = useTranslations('common');

  const { mutate: deleteDirectoryEntity } =
    useDeleteDirectoryEntity(directoryEntity);
  const directoryEditPathnames = extractDirectoryEditPathnames(pathnames);
  const pathname = directoryEditPathnames[directoryEntity];

  return (
    <div className="flex items-center justify-center gap-x-3">
      <Button asChild size="icon">
        <Link
          data-testid={`edit-${directoryEntity}`}
          href={{
            params: { id: row.original.id },
            pathname,
          }}
        >
          <FaEdit className="h-4 w-4" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={() => {
          deleteDirectoryEntity(
            typeof row.original.id === 'number'
              ? String(row.original.id)
              : row.original.id,
          );
        }}
        title={tCommon('deleteItemQuestion', { item: row.original.text })}
        trigger={
          <Button
            size="icon"
            variant="destructive"
            data-testid={`delete-${directoryEntity}`}
          >
            <MdDeleteForever className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
};
