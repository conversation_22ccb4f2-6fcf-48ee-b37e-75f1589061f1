import { rooms } from '@rie/db-schema/schemas';
import type { RoomInput } from '@rie/domain/types';
import { PgDatabaseLive } from '@rie/postgres-db';
import { eq } from 'drizzle-orm';
import * as Effect from 'effect/Effect';

export class RoomsRepositoryLive extends Effect.Service<RoomsRepositoryLive>()(
  'RoomsRepositoryLive',
  {
    dependencies: [PgDatabaseLive.Default],
    effect: Effect.gen(function* () {
      const dbClient = yield* PgDatabaseLive;

      const findAllRooms = dbClient.makeQuery((execute) => {
        return execute((client) =>
          client.query.rooms.findMany({
            columns: {
              id: true,
              number: true,
              area: true,
              floorLoad: true,
              buildingId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              building: {
                columns: { id: true },
                with: {
                  translations: {
                    columns: {
                      id: true,
                      locale: true,
                      name: true,
                    },
                  },
                },
              },
            },
          }),
        );
      });

      const findRoomById = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client.query.rooms.findFirst({
            where: eq(rooms.id, id),
            columns: {
              id: true,
              number: true,
              area: true,
              floorLoad: true,
              buildingId: true,
              createdAt: true,
              updatedAt: true,
              modifiedBy: true,
            },
            with: {
              building: {
                columns: { id: true },
              },
            },
          }),
        );
      });

      const createRoom = (params: { room: RoomInput }) => {
        return dbClient.transaction((tx) => {
          return tx((client) =>
            client
              .insert(rooms)
              .values({
                ...params.room,
              })
              .returning({
                id: rooms.id,
                number: rooms.number,
                area: rooms.area,
                floorLoad: rooms.floorLoad,
                buildingId: rooms.buildingId,
                createdAt: rooms.createdAt,
                updatedAt: rooms.updatedAt,
                modifiedBy: rooms.modifiedBy,
              }),
          );
        });
      };

      const updateRoom = (params: { roomId: string; room: RoomInput }) => {
        return dbClient.transaction((tx) => {
          return tx((client) =>
            client
              .update(rooms)
              .set({
                ...params.room,
              })
              .where(eq(rooms.id, params.roomId))
              .returning({
                id: rooms.id,
                number: rooms.number,
                area: rooms.area,
                floorLoad: rooms.floorLoad,
                buildingId: rooms.buildingId,
                createdAt: rooms.createdAt,
                updatedAt: rooms.updatedAt,
                modifiedBy: rooms.modifiedBy,
              }),
          );
        });
      };

      const deleteRoom = dbClient.makeQuery((execute, id: string) => {
        return execute((client) =>
          client
            .delete(rooms)
            .where(eq(rooms.id, id))
            .returning({ id: rooms.id }),
        );
      });

      return {
        findAllRooms,
        findRoomById,
        createRoom,
        updateRoom,
        deleteRoom,
      } as const;
    }),
  },
) {}
