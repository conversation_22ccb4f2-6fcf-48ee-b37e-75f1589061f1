import type { RieServiceParams } from '@/constants/rie-client';
import type { ApiInfiniteReturnType } from '@/types/common';
import type { InfrastructureFull } from '@/types/infrastructure';
import { queryOptions, useQuery } from '@tanstack/react-query';
import type { UseQueryOptions } from '@tanstack/react-query';
import type { AxiosError } from 'axios';
import { useAppStore } from '@/providers/app-store-provider';
import { getInfrastructuresList } from '@/services/infrastructures';

type infrastructuresListArgs<TData> = {
  pageParam: number; //offset
  params: RieServiceParams;
  queryParams?: string;
  viewOwnInfrastructures: boolean;
} & Pick<
  UseQueryOptions<
    ApiInfiniteReturnType<InfrastructureFull[]>,
    AxiosError,
    TData
  >,
  'select'
>;

export const infrastructuresListOptions = <TData>({
  pageParam,
  params,
  queryParams,
  select,
  viewOwnInfrastructures,
}: infrastructuresListArgs<TData>) => {
  return queryOptions({
    queryFn: () =>
      getInfrastructuresList({
        pageParam,
        params,
        queryParams: queryParams ?? '',
        viewOwnInfrastructures,
      }),
    queryKey: [
      'infrastructuresList',
      params,
      ...(queryParams?.length ? [{ queryParams }] : []),
      pageParam,
      viewOwnInfrastructures,
    ],
    select,
  });
};

type UseGetInfrastructuresListArgs<TData> = {
  pageParam?: number;
  params: RieServiceParams;
  queryParams?: string;
} & Pick<
  UseQueryOptions<
    ApiInfiniteReturnType<InfrastructureFull[]>,
    AxiosError,
    TData
  >,
  'select'
>;

export const useGetInfrastructuresList = <TData>({
  pageParam = 0,
  params,
  queryParams,
  select,
}: UseGetInfrastructuresListArgs<TData>) => {
  const storeQueryParams = useAppStore((state) => state.queryParams);
  const viewOwnInfrastructures = useAppStore((state) => state.viewOwnResources);

  // Use passed queryParams if provided, otherwise fall back to store
  const finalQueryParams = queryParams ?? storeQueryParams;

  return useQuery(
    infrastructuresListOptions({
      pageParam,
      params,
      queryParams: finalQueryParams,
      select,
      viewOwnInfrastructures,
    }),
  );
};
