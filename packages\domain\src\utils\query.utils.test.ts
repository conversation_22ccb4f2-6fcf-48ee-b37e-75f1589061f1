import {
  DEFAULT_PAGINATION_LIMIT,
  DEFAULT_PAGINATION_PAGE,
  DEFAULT_SORT_COLUMN,
  DEFAULT_SORT_ORDER,
} from '@rie/constants/pagination';
import { Option } from 'effect';
import { describe, expect, it } from 'vitest';
import {
  buildQueryConfig,
  createSortConfig,
  getOffsetFromPagination,
  getPaginationConfigFromQueryParams,
  getSortConfigFromQueryParams,
} from './query.utils';

describe('query.utils', () => {
  describe('createSortConfig', () => {
    it('should return default sort config when no params provided', () => {
      const result = createSortConfig();
      expect(result).toEqual({
        column: DEFAULT_SORT_COLUMN,
        order: DEFAULT_SORT_ORDER,
      });
    });

    it('should use provided column and keep default order', () => {
      const result = createSortConfig({ column: 'name' });
      expect(result).toEqual({
        column: 'name',
        order: DEFAULT_SORT_ORDER,
      });
    });

    it('should use provided order and keep default column', () => {
      const result = createSortConfig({ order: 'asc' });
      expect(result).toEqual({
        column: DEFAULT_SORT_COLUMN,
        order: 'asc',
      });
    });

    it('should use both provided column and order', () => {
      const result = createSortConfig({ column: 'name', order: 'asc' });
      expect(result).toEqual({
        column: 'name',
        order: 'asc',
      });
    });
  });

  describe('getSortConfigFromQueryParams', () => {
    it('should return default config when no sort param provided', () => {
      const result = getSortConfigFromQueryParams();
      expect(result).toEqual({
        column: DEFAULT_SORT_COLUMN,
        order: DEFAULT_SORT_ORDER,
      });
    });

    it('should parse valid sort param with brackets', () => {
      const result = getSortConfigFromQueryParams('[createdAt]=desc');
      expect(result).toEqual({
        column: 'createdAt',
        order: 'desc',
      });
    });

    it('should parse valid sort param without brackets', () => {
      const result = getSortConfigFromQueryParams('createdAt=asc');
      expect(result).toEqual({
        column: 'createdAt',
        order: 'asc',
      });
    });

    it('should return default config for invalid sort param format', () => {
      const result = getSortConfigFromQueryParams('invalid-format');
      expect(result).toEqual({
        column: DEFAULT_SORT_COLUMN,
        order: DEFAULT_SORT_ORDER,
      });
    });

    it('should return default config for empty column name', () => {
      const result = getSortConfigFromQueryParams('[]=desc');
      expect(result).toEqual({
        column: DEFAULT_SORT_COLUMN,
        order: DEFAULT_SORT_ORDER,
      });
    });
  });

  describe('getPaginationConfigFromQueryParams', () => {
    it('should return default pagination when no params provided', () => {
      const result = getPaginationConfigFromQueryParams();
      expect(result).toEqual({
        page: DEFAULT_PAGINATION_PAGE,
        limit: DEFAULT_PAGINATION_LIMIT,
      });
    });

    it('should return only limit when limit is -1', () => {
      const result = getPaginationConfigFromQueryParams({ limit: -1 });
      expect(result).toEqual({ limit: -1 });
    });

    it('should use provided page and default limit', () => {
      const result = getPaginationConfigFromQueryParams({ page: 2 });
      expect(result).toEqual({
        page: 2,
        limit: DEFAULT_PAGINATION_LIMIT,
      });
    });

    it('should use provided limit and default page', () => {
      const result = getPaginationConfigFromQueryParams({ limit: 20 });
      expect(result).toEqual({
        page: DEFAULT_PAGINATION_PAGE,
        limit: 20,
      });
    });

    it('should use both provided page and limit', () => {
      const result = getPaginationConfigFromQueryParams({ page: 3, limit: 15 });
      expect(result).toEqual({
        page: 3,
        limit: 15,
      });
    });
  });

  describe('getOffsetFromPagination', () => {
    it('should return None when limit is -1', () => {
      const result = getOffsetFromPagination({ limit: -1 });
      expect(Option.isNone(result)).toBe(true);
    });

    it('should calculate offset for first page', () => {
      const result = getOffsetFromPagination({ page: 1, limit: 10 });
      expect(Option.isSome(result)).toBe(true);
      expect(Option.getOrNull(result)).toBe(0);
    });

    it('should calculate offset for second page', () => {
      const result = getOffsetFromPagination({ page: 2, limit: 10 });
      expect(Option.isSome(result)).toBe(true);
      expect(Option.getOrNull(result)).toBe(10);
    });

    it('should use default values when no params provided', () => {
      const result = getOffsetFromPagination();
      expect(Option.isSome(result)).toBe(true);
      expect(Option.getOrNull(result)).toBe(
        (DEFAULT_PAGINATION_PAGE - 1) * DEFAULT_PAGINATION_LIMIT,
      );
    });

    it('should handle custom page and limit values', () => {
      const result = getOffsetFromPagination({ page: 3, limit: 15 });
      expect(Option.isSome(result)).toBe(true);
      expect(Option.getOrNull(result)).toBe(30); // (3-1) * 15
    });
  });

  describe('buildQueryConfig', () => {
    it('should build config with default values when no params provided', () => {
      const result = buildQueryConfig({
        locale: 'fr',
        view: 'list',
      });
      expect(result).toEqual({
        locale: 'fr',
        view: 'list',
        sort: {
          column: DEFAULT_SORT_COLUMN,
          order: DEFAULT_SORT_ORDER,
        },
        pagination: Option.some({
          limit: DEFAULT_PAGINATION_LIMIT,
          offset: 0,
        }),
      });
    });

    it('should build config with custom sort', () => {
      const result = buildQueryConfig({
        locale: 'fr',
        view: 'list',
        sort: '[name]=asc',
      });
      expect(result).toEqual({
        locale: 'fr',
        view: 'list',
        sort: {
          column: 'name',
          order: 'asc',
        },
        pagination: Option.some({
          limit: DEFAULT_PAGINATION_LIMIT,
          offset: 0,
        }),
      });
    });

    it('should build config with custom pagination', () => {
      const result = buildQueryConfig({
        locale: 'fr',
        view: 'list',
        page: 2,
        limit: 20,
      });
      expect(result).toEqual({
        locale: 'fr',
        view: 'list',
        sort: {
          column: DEFAULT_SORT_COLUMN,
          order: DEFAULT_SORT_ORDER,
        },
        pagination: Option.some({
          limit: 20,
          offset: 20,
        }),
      });
    });

    it('should handle no pagination when limit is -1', () => {
      const result = buildQueryConfig({
        locale: 'fr',
        view: 'list',
        limit: -1,
      });
      expect(result).toEqual({
        locale: 'fr',
        view: 'list',
        sort: {
          column: DEFAULT_SORT_COLUMN,
          order: DEFAULT_SORT_ORDER,
        },
        pagination: Option.none(),
      });
    });

    it('should build config with both custom sort and pagination', () => {
      const result = buildQueryConfig({
        locale: 'fr',
        view: 'list',
        sort: '[updatedAt]=desc',
        page: 3,
        limit: 15,
      });
      expect(result).toEqual({
        locale: 'fr',
        view: 'list',
        sort: {
          column: 'updatedAt',
          order: 'desc',
        },
        pagination: Option.some({
          limit: 15,
          offset: 30,
        }),
      });
    });
  });
});
