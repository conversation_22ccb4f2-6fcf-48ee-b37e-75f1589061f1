import {
  getGenericByIdOptions,
  getGenericListOptions,
} from '@/hooks/bottin/generic-list.options';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllPeopleOptions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) =>
  getGenericListOptions({ controlledListKey: 'person', view } as {
    controlledListKey: 'person';
    view: View;
  });

export const getPersonByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) => getGenericByIdOptions({ controlledListKey: 'person', id, view } as const);
