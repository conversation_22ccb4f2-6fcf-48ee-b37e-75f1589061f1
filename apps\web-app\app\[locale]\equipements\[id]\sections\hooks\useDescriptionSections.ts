import type { DetailSectionProps } from '@/app/[locale]/equipements/[id]/sections/types/detail-type';
import type { DetailsSectionInput } from '@/components/details-section-body/types';
import { useUserHasPermissionTo } from '@/hooks/useUserHasPermissionTo';
import type { EquipmentDescriptionDetailed } from '@/types/equipment';
import { useTranslations } from 'next-intl';

export const useDescriptionSections = ({
  accessories,
  comments,
  components,
  decommissionDate,
  detailerWebsites,
  disposalTerms,
  estimatedLifetime,
  excellencePoles,
  fundingProjects,
  healthCanadaCompliance,
  holder,
  installationDate,
  inventoryNumber,
  jurisdiction,
  location,
  mainEquipments,
  manufacturer,
  manufacturingYear,
  measurementUncertainty,
  model,
  parentInfrastructure,
  purchaseDate,
  relatedEquipments,
  researchDomains,
  safetyManagers,
  safetyRisks,
  scientificPublications,
  socioEconomicObjectives,
  specifications,
  supplier,
  technicalManagers,
  techniques,
  usageContexts,
}: EquipmentDescriptionDetailed) => {
  const t = useTranslations('equipments.details.sections.description');
  const { permissionStatus } = useUserHasPermissionTo({
    action: 'read',
    // TODO: Previous value was 'equipment-location', however because `resourceType` doesn't support 'equipment-location'
    // When migrating this form to the new API we don't need to worry about this since the API will
    // only send field if the user has the right permission
    resourceType: 'equipment',
    resourceId: '1', // TODO: This is just to satisfy the hook arguments
  });
  const hasLocationPermission = permissionStatus === 'granted';

  const identificationSections: DetailSectionProps[] = [
    { label: t('identification.fields.model'), value: model },
    { label: t('identification.fields.manufacturer'), value: manufacturer },
    { label: t('identification.fields.supplier'), value: supplier },
    ...(hasLocationPermission
      ? [{ label: t('identification.fields.location'), value: location }]
      : []),
    {
      label: t('identification.fields.InventoryNumber'),
      value: inventoryNumber,
    },
    { label: t('identification.fields.jurisdiction'), value: jurisdiction },
    { label: t('identification.fields.holder'), value: holder },
    {
      label: t('identification.fields.relatedInfrastructure'),
      value: parentInfrastructure,
    },
  ];

  const specificationsSections: DetailSectionProps[] = [
    { label: t('specifications.fields.specifications'), value: specifications },
  ];

  const usageContextsSections: DetailSectionProps[] = [
    { label: t('usageContext.fields.usageContext'), value: usageContexts },
    { label: t('usageContext.fields.researchDomain'), value: researchDomains },
    {
      label: t('usageContext.fields.socioEconomicObjectives'),
      value: socioEconomicObjectives,
    },
    {
      label: t('usageContext.fields.poleOfExcellence'),
      value: excellencePoles,
    },
    { label: t('usageContext.fields.techniques'), value: techniques },
  ];

  const relatedEquipmentsSections: DetailSectionProps[] = [
    {
      label: t('relatedEquipments.fields.mainEquipments'),
      value: mainEquipments,
    },
    { label: t('relatedEquipments.fields.components'), value: components },
    { label: t('relatedEquipments.fields.accessories'), value: accessories },
    {
      label: t('relatedEquipments.fields.relatedEquipments'),
      value: relatedEquipments,
    },
  ];

  const managersSections: DetailSectionProps[] = [
    {
      label: t('managers.fields.technicalManagers', {
        count: technicalManagers?.length ?? 0,
      }),
      value: technicalManagers,
    },
    {
      label: t('managers.fields.safetyManagers', {
        count: safetyManagers?.length ?? 0,
      }),
      value: safetyManagers,
    },
  ];

  const fundingSections: DetailSectionProps[] = [
    {
      label: t('financing.fields.fundingProjects'),
      value: fundingProjects,
    },
  ];

  const otherInfoSections: DetailSectionProps[] = [
    { label: t('otherInfo.fields.comments'), value: comments },
    { label: t('otherInfo.fields.safetyRisks'), value: safetyRisks },
    {
      label: t('otherInfo.fields.measurementUncertainty'),
      value: measurementUncertainty,
    },
  ];

  const equipmentLifeSections: DetailSectionProps[] = [
    {
      label: t('equipmentLife.fields.manufacturingYear'),
      value: manufacturingYear,
    },
    { label: t('equipmentLife.fields.purchaseDate'), value: purchaseDate },
    {
      label: t('equipmentLife.fields.installationDate'),
      value: installationDate,
    },
    {
      label: t('equipmentLife.fields.estimatedLifetime'),
      value: estimatedLifetime,
    },
    {
      label: t('equipmentLife.fields.decommissionDate'),
      value: decommissionDate,
    },
    { label: t('equipmentLife.fields.disposalTerms'), value: disposalTerms },
    {
      label: t('equipmentLife.fields.detailerWebsites'),
      value: detailerWebsites,
    },
    {
      label: t('equipmentLife.fields.healthCanadaCompliance'),
      value: healthCanadaCompliance,
    },
  ];

  const scientificPublicationsSections: DetailSectionProps[] = [
    {
      label: t('scientificPublications.fields.scientificPublications'),
      value: scientificPublications,
    },
  ];

  return [
    {
      body: identificationSections,
      header: t('identification.header'),
    },
    {
      body: specificationsSections,
      header: t('specifications.header'),
    },
    {
      body: usageContextsSections,
      header: t('usageContext.header'),
    },
    {
      body: relatedEquipmentsSections,
      header: t('relatedEquipments.header'),
    },
    {
      body: managersSections,
      header: t('managers.header'),
    },
    {
      body: fundingSections,
      header: t('financing.header'),
    },
    {
      body: otherInfoSections,
      header: t('otherInfo.header'),
    },
    {
      body: equipmentLifeSections,
      header: t('equipmentLife.header'),
    },
    {
      body: scientificPublicationsSections,
      header: t('scientificPublications.header'),
    },
  ] satisfies DetailsSectionInput[];
};
