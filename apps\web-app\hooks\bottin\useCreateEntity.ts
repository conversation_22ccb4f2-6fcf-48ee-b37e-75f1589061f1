import { createEntity } from '@/services/bottin/entities';
import type { EntityPayload } from '@/types/bottin/directory';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import type { DirectoryEntity } from '@rie/domain/types';

export const useCreateEntity = <K extends DirectoryEntity>(
  directoryEntity: K,
  onSuccess: (id: string) => void,
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: EntityPayload<K>) =>
      createEntity(directoryEntity, payload),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['allInfrastructures'] }); //TODO: changer par le bon nom de query a invalider
      queryClient.invalidateQueries({ queryKey: ['infiniteEquipments'] });
      onSuccess(data.id.toString());
    },
  });
};
