{"name": "@rie/constants", "version": "1.0.0", "type": "module", "description": "Constants package", "main": "./build/index.js", "module": "./build/index.js", "types": "./build/dts/index.d.ts", "exports": {".": {"types": "./build/dts/index.d.ts", "import": "./build/index.js", "require": "./build/index.js"}, "./pagination": {"types": "./build/dts/pagination/index.d.ts", "import": "./build/pagination/index.js", "require": "./build/pagination/index.js"}, "./session": {"types": "./build/dts/session/index.d.ts", "import": "./build/session/index.js", "require": "./build/session/index.js"}}, "files": ["build", "build/dts"], "scripts": {"build": "tsc -b tsconfig.build.json && tsc-alias -p tsconfig.build.json", "dev": "tsc -b tsconfig.src.json -w && tsc-alias -p tsconfig.build.json", "type-check": "tsc -b tsconfig.build.json", "lint": "pnpm biome check --write"}, "dependencies": {"@rie/biome-config": "workspace:*"}, "devDependencies": {"tsc-alias": "^1.8.16", "typescript": "^5.8.3"}}