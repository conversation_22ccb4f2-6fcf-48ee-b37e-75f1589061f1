import { PgDatabaseLayer } from '@rie/postgres-db';
import { CampusesRepositoryLive } from '@rie/repositories';
import { CampusesServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const CampusesServicesLayer = Layer.mergeAll(
  CampusesRepositoryLive.Default,
  CampusesServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const CampusesRuntime = ManagedRuntime.make(CampusesServicesLayer);
