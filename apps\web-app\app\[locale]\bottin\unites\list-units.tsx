'use client';

import { unitsColumns } from '@/app/[locale]/bottin/unites/columns';
import { GenericList } from '@/components/bottin-table/generic-list';
import { initialColumnVisibility } from '@/constants/bottin/units';
import type { SupportedLocale } from '@/types/locale';
import type { UnitList } from '@rie/domain/types';

type UnitsListProps = {
  locale: SupportedLocale;
};

export const UnitsList = ({ locale }: UnitsListProps) => {
  return (
    <GenericList<'unit', 'list', UnitList, Record<string, unknown>>
      controlledListKey="unit"
      view="list"
      locale={locale}
      columns={unitsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="units"
    />
  );
};
