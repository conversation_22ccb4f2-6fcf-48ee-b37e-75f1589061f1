import { PgDatabaseLayer } from '@rie/postgres-db';
import { PeopleRepositoryLive } from '@rie/repositories';
import { PeopleServiceLive } from '@rie/services';
import * as Layer from 'effect/Layer';
import * as ManagedRuntime from 'effect/ManagedRuntime';

const PeopleServicesLayer = Layer.mergeAll(
  PeopleRepositoryLive.Default,
  PeopleServiceLive.Default,
).pipe(Layer.provide(PgDatabaseLayer));

export const PeopleRuntime = ManagedRuntime.make(PeopleServicesLayer);
