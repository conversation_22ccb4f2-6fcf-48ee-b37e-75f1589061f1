import * as fs from 'node:fs';
import * as path from 'node:path';
import { env } from '@/env';
import { drizzle } from 'drizzle-orm/node-postgres';
import { migrate } from 'drizzle-orm/node-postgres/migrator';
import { Pool } from 'pg';

// Main migration function
async function runMigrations() {
  console.log('Connecting to database...');
  const pool = new Pool({
    connectionString: env.PG_DATABASE_URL,
  });

  const db = drizzle(pool);

  // Run standard Drizzle migrations
  console.log('Running Drizzle migrations...');
  await migrate(db, {
    migrationsSchema: './src/schemas/index.ts',
    migrationsFolder: './src/migrations',
  });

  // Apply custom triggers
  console.log('Applying custom triggers...');

  // Default role trigger
  const defaultRoleTriggerSql = fs.readFileSync(
    path.join(__dirname, 'migrations/triggers/default_role_trigger.sql'),
    'utf8',
  );
  await pool.query(defaultRoleTriggerSql);

  // Link user to person trigger
  const linkUserToPersonTriggerSql = fs.readFileSync(
    path.join(__dirname, 'migrations/triggers/link_user_to_person_trigger.sql'),
    'utf8',
  );
  await pool.query(linkUserToPersonTriggerSql);

  console.log('All migrations and triggers applied successfully!');
  await pool.end();
}

// Run migrations
runMigrations().catch((err) => {
  console.error('Migration failed:', err);
  process.exit(1);
});
