import { EndpointRuntime } from '@/infrastructure/runtimes/endpoint.runtime';
import { effectValidator } from '@hono/effect-validator';
import { Schemas } from '@rie/domain';
import { ResourceIdSchema } from '@rie/domain/schemas';
import { ServiceOfferServiceLive } from '@rie/services';
import { Cause, Effect, Exit, Schema } from 'effect';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

import { DatabaseError } from 'pg';

export const createServiceOfferRoute = describeRoute({
  description: 'Crée une offre de service',
  operationId: 'createServiceOffer',
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(Schemas.ServiceOfferResponseSchema),
        },
      },
      description: 'Offre de service créée avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
  },
  tags: ['Offres de service'],
});

// Get service offer route
export const getServiceOfferRoute = describeRoute({
  description: 'Obtient une offre de service par son ID',
  operationId: 'getServiceOffer',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: "ID de l'offre de service",
    },
    {
      name: 'locale',
      in: 'query',
      required: true,
      schema: resolver(Schema.String),
      description:
        "Langue des traductions pour l'offre de service (exemple: fr, en)",
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schemas.ServiceOfferResponseSchema),
        },
      },
      description: "Retourne l'offre de service correspondante",
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Offre de service non trouvée',
    },
  },
  tags: ['Offres de service'],
});

const serviceOfferRoute = new Hono();

// serviceOfferRoute.post(
// 	'/',
// 	createServiceOfferRoute,
// 	effectValidator('json', CreateServiceOfferSchema),
// 	(ctx) => {
// 		return Effect.gen(function* (_) {
// 			const serviceOfferData = ctx.req.valid('json');
// 			const serviceOfferService = yield* _(ServiceOfferService);
// 			const createdServiceOffer = yield* _(
// 				serviceOfferService.create(serviceOfferData),
// 			);
// 			return ctx.json({
// 				serviceOffer: createdServiceOffer,
// 			});
// 		}).pipe(EndpointRuntime.runPromise);
// 	},
// );

serviceOfferRoute.get(
  '/:id',
  getServiceOfferRoute,
  effectValidator('query', Schemas.LocaleQuerySchema),
  async (ctx) => {
    const program = Effect.gen(function* () {
      const { locale } = ctx.req.valid('query');

      const serviceOfferService = yield* ServiceOfferServiceLive;
      return yield* serviceOfferService.findById(ctx.req.param('id'), locale);
    });

    const maybeServiceOffer = await EndpointRuntime.runPromiseExit(program);

    if (Exit.isFailure(maybeServiceOffer)) {
      const squashed = Cause.squash(maybeServiceOffer.cause);

      if (squashed instanceof Cause.NoSuchElementException) {
        return ctx.json({ error: 'Service Offer not found' }, 404);
      }

      if (squashed instanceof DatabaseError) {
        return ctx.json({ error: squashed.message }, 400);
      }

      return ctx.json({ error: 'An error occurred' }, 500);
    }

    if (Exit.isSuccess(maybeServiceOffer)) {
      return ctx.json({ serviceOffer: maybeServiceOffer.value });
    }
  },
);

export default serviceOfferRoute;
