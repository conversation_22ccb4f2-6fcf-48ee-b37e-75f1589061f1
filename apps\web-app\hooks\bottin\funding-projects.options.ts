import {
  getGenericByIdOptions,
  getGenericListOptions,
} from '@/hooks/bottin/generic-list.options';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllFundingProjectsOptions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) =>
  getGenericListOptions({ controlledListKey: 'fundingProjects', view } as {
    controlledListKey: 'fundingProjects';
    view: View;
  });

export const getFundingProjectByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) =>
  getGenericByIdOptions({
    controlledListKey: 'fundingProjects',
    id,
    view,
  } as const);
