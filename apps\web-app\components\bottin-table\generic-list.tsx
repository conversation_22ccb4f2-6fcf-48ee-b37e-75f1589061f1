'use client';

import { BottinTable } from '@/components/bottin-table/bottin-table';
import { useGetGenericList } from '@/hooks/bottin/generic-list.hook';
import type { ControlledListKey } from '@/types/common';
import type { SupportedLocale } from '@/types/locale';
import type { CollectionViewParamType } from '@rie/domain/types';
import type { ColumnDef, VisibilityState } from '@tanstack/react-table';

type GenericListProps<
  Key extends ControlledListKey,
  View extends CollectionViewParamType['view'],
  T extends object,
  C extends object,
> = {
  controlledListKey: Key;
  view: View;
  locale: SupportedLocale;
  columns: ColumnDef<T, C>[];
  initialColumnVisibility: VisibilityState;
  resourceName: string;
};

export const GenericList = <
  Key extends ControlledListKey,
  View extends CollectionViewParamType['view'],
  T extends object,
  C extends object,
>({
  controlledListKey,
  view,
  locale,
  columns,
  initialColumnVisibility,
  resourceName,
}: GenericListProps<Key, View, T, C>) => {
  const { data, isLoading } = useGetGenericList({
    controlledListKey,
    view,
  });

  return (
    <BottinTable
      columns={columns}
      data={(data ?? []) as T[]}
      initialColumnVisibility={initialColumnVisibility}
      isLoading={isLoading}
      resourceName={resourceName}
    />
  );
};
