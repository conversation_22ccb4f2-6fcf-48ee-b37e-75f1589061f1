import {
  getPostLocalizedValue,
  mapFormFieldToId,
} from '@/helpers/resources.helpers';
import type { EstablishmentFormSchema } from '@/schemas/bottin/establishment-form-schema';
import type { EntityPayload } from '@/types/bottin/directory';
import type { EstablishmentPostPayload } from '@/types/bottin/establishement';
import type { IdType } from '@/types/bottin/project';
import { isSelectOption } from '@/types/common';

export const mapEstablishmentBaseForm = (
  formData: EstablishmentFormSchema,
): EstablishmentPostPayload => ({
  names: getPostLocalizedValue(formData.name),
  typeEtablissement: { id: formData.establishmentType.value || '' },
  pseudonym: getPostLocalizedValue(formData.pseudonym),
  acronyms: getPostLocalizedValue(formData.acronym),
  affiliations:
    formData.affiliatedPersons && formData.affiliatedPersons.length > 0
      ? formData.affiliatedPersons.reduce<
          {
            fonction: string;
            person: IdType;
            typeFonction: IdType;
          }[]
        >((acc, person) => {
          if (isSelectOption(person.person) && isSelectOption(person.jobType)) {
            acc.push({
              fonction: person.jobTitle,
              person: mapFormFieldToId(person.person),
              typeFonction: mapFormFieldToId(person.jobType),
            });
          }

          return acc;
        }, [])
      : [],
});

export const mapFormDataToPostPutPayload = (
  formData: EstablishmentFormSchema,
): EntityPayload<'institution'> => {
  return mapEstablishmentBaseForm(formData);
};
