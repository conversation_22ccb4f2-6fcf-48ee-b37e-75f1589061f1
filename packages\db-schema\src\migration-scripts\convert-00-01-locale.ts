import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { SQL_FILE_NAME } from './constants';
import { LocaleMigrationConverter } from './converters/00-01-convert-locale';

async function convertLocaleData() {
  const scriptDir = __dirname;
  // Output will go to migration-scripts/output
  const outputDir = path.join(scriptDir, 'output');
  const outputFile = path.join(outputDir, SQL_FILE_NAME);

  try {
    // Create output directory if it doesn't exist
    await fs.mkdir(outputDir, { recursive: true });

    // Initialize converter
    const converter = new LocaleMigrationConverter();

    // Convert the file
    await converter.convertFile(outputFile);

    console.log('Conversion completed successfully!');
    console.log(`Output written to: ${outputFile}`);
  } catch (error) {
    console.error('Error during conversion:', error);
    process.exit(1);
  }
}

// Run the conversion
convertLocaleData().catch(console.error);
