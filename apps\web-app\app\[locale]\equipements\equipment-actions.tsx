import { EntityActions } from '@/components/entity-actions/entity-actions';
import { useDeleteEquipment } from '@/hooks/equipment/useDeleteEquipment';
import type { EquipmentList } from '@rie/domain/types';
import type { Row } from '@tanstack/react-table';

type EquipmentActionsProps = { row: Row<EquipmentList> };

export const EquipmentActions = ({ row }: EquipmentActionsProps) => {
  const { mutate: deleteEquipment } = useDeleteEquipment();

  return (
    <EntityActions
      row={row}
      config={{
        resource: 'equipment',
        basePath: '/equipements',
        deleteHook: deleteEquipment,
      }}
    />
  );
};
