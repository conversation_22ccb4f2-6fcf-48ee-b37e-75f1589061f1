{"About": "About", "Data": "RIÉ Data", "Decision tools": "Decision tools", "Eco responsibility": "Eco-responsibility", "Fast Access": "Fast Access", "Glossary": "Glossary", "How to use RIE": "How to use RIÉ?", "News": "News", "location": "Location", "manufacturer": "Manufacturer", "Registry": "Consult the registry", "Resources": "Resources", "actions": {"created": "Created", "deleted": "Deleted", "deletedFeminine": "Deleted", "updated": "Updated"}, "add": "Add", "addEquipment": "Add an equipment", "addResource": "Add {resource}", "addTranslation": "Add a translation", "applyResource": "Apply {resource}", "backHome": "Back to home", "backToResults": "Back to results", "buildingsCount": "{count, plural, =0 {No building} =1 {building} other {# buildings}} accessible", "by": "by", "campusCount": "{count, plural, =0 {No campus} =1 {campus} other {# campuses}} accessible", "cancel": "Cancel", "categoryPlural": "{count, plural, =0 {} =1 {Category} other {Categories}}", "columns": "Columns", "components": {"address": {"title": "Address", "addressType": {"label": "Contact type", "campus": "Campus", "civicAddress": "Civic address"}, "campus": {"title": "Address in the campus", "building": {"label": "Building", "placeholder": "Select a building", "error": {"required": "Please select a building"}}, "room": {"label": "Local", "placeholder": "Select a room", "awaitingForBuildingSelection": "Select a building to be able to select a room", "noRoomsAssociatedWithBuilding": "There are no rooms associated with the selected building"}}, "civicAddress": {"label": "Address", "error": {"required": "Please enter an address", "notFound": "Address not found"}}, "addAddress": "Add an address"}}, "contract": "Contract", "contractCount": "{count, plural, =0 {No contract} =1 {contract} other {# contracts}} accessible", "count": "Count is at", "createdAt": "Created at", "delete": "Delete", "deleteItemQuestion": "Est-ce que vous êtes sur de vouloir supprimer {item}?", "deleteResource": "Delete {resource}", "description": "Description", "directory": {"manufacturers": "manufacturers", "establishments": "establishments", "units": "units", "people": "people", "rooms": "rooms", "contracts": "contracts", "buildings": "buildings", "campus": "campus", "financingProjects": "financing projects"}, "document": "Document", "edit": "Edit", "en": "English", "equipment": "Equipment", "equipmentCount": "{count, plural, =0 {No equipment accessible} =1 { equipment accessible} other {# equipments accessible}}", "equipments": "Equipments", "establishmentsCount": "{count, plural, =0 {No establishment} =1 {# establishment} other {# establishments}} accessible", "file": "File", "files": "Documents", "filterResults": "Filter results", "filters": "Filters", "financingProjectsCount": "{count, plural, =0 {No financing project} =1 {financing project} other {# financing projects}} accessible", "fr": "French", "fundingProjectsCount": "{count, plural, =0 {No financing project} =1 {financing project} other {# financing projects}} accessible", "goTo": "Go to", "home": "Home", "image": "Image", "images": "Images", "infrastructure": "Infrastructure", "infrastructureCount": "{count, plural, =0 {No infrastructures accessible} =1 {# infrastructure accessible} other {# infrastructures accessible}}", "infrastructures": "Infrastructures", "languageSwitcher": {"label": "Select language"}, "loading": "Loading ...", "manufacturersCount": "{count, plural, =0 {No manufacturer} =1 {# manufacturer} other {# manufacturers}} accessible", "missing": "Missing ({count})", "missingSelected": {"infrastructure": "Missing infrastructure", "equipment": "Missing equipment", "manufacturer": "Missing manufacturer", "establishment": "Missing establishment", "unit": "Missing unit", "person": "Missing person", "room": "Missing room", "building": "Missing building", "campus": "Missing campus", "contract": "Missing contract", "financingProject": "Missing financing project", "default": "Missing", "category": "Missing category", "researchDomain": "Missing research domain", "supplier": "Missing supplier", "technique": "Missing technique", "poleExcellences": "Missing pole of excellence", "SecteurApplications": "Missing sector of application"}, "model": "Model", "noResults": "No results", "notifications": {"errors": {"generic": "An error occurred", "onAction": "Failed to {action} {resource}.", "onFetchResource": "Failed to fetch {resource}.", "onSaveResource": "Failed to save {resource}.", "onDeleteResource": "Failed to delete {resource}.", "onCreateResource": "Failed to create {resource}.", "onUpdateResource": "Failed to update {resource}."}, "successTitle": "Success", "errorTitle": "Error", "success": "{resource} {action} successfully."}, "ok": "OK", "operations": {"create": "Create", "delete": "Delete", "update": "Edit", "read": "View", "fetch": "Get"}, "page": "Page", "pagination": {"goToPage": "Go to page", "rowsPerPage": "Rows per page", "page": "Page", "of": "of", "go": "Go"}, "peopleCount": "{count, plural, =0 {No people} =1 {# person} other {# people}} accessible", "permanentDeleteAction": "This action is permanent and cannot be undone.", "pickDate": "Pick a date", "roomsCount": "{count, plural, =0 {No rooms} =1 {# room} other {# rooms }} accessible", "removeTranslation": "Remove translation", "resetResource": "Reset {resource}", "resources": {"building": "Building", "buildings": "Buildings", "directory": "Directory", "equipment": "Equipment", "equipments": "Equipments", "infrastructure": "Infrastructure", "infrastructures": "Infrastructures", "manufacturer": "Manufacturer", "manufacturers": "Manufacturers", "establishment": "Establishment", "establishments": "Establishments", "unit": "Unit", "units": "Units", "room": "Room", "rooms": "Rooms", "people": "Person", "peoples": "People", "contract": "Contract", "contracts": "Contracts", "campus": "Campus", "campuses": "Campuses", "financingProject": "Financing project", "financingProjects": "Financing projects", "controlledList": {"technique": "Technic"}}, "save": "Save", "saving": "Saving", "search": "Search", "searchEquipment": "Search for an equipment", "searchInfrastructure": "Search for an infrastructure", "select": "Select", "selectEntityToEnable": "Select {entity} to enable this field", "show": "Show", "sort": {"sortBy": "Sort by", "relevance": "Relevance", "mostRecent": "Most recent", "lessRecent": "Less recent"}, "unitsCount": "{count, plural, =0 {No unit} =1 {# unit} other {# units}} accessible", "update": "Update", "updatedAt": "Updated at", "url": "URL", "video": "Video", "videos": "Vid<PERSON><PERSON>", "view": "View", "viewAllResources": "All {resources}", "viewOwnResources": "My {resources} only", "category": "Category"}