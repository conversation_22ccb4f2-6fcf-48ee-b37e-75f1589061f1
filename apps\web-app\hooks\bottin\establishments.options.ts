import {
  getGenericByIdOptions,
  getGenericListOptions,
} from '@/hooks/bottin/generic-list.options';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllEstablishmentsOptions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) =>
  getGenericListOptions({ controlledListKey: 'establishment', view } as {
    controlledListKey: 'establishment';
    view: View;
  });

export const getEstablishmentByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) =>
  getGenericByIdOptions({
    controlledListKey: 'establishment',
    id,
    view,
  } as const);
