import { handleEffectError } from '@/api/v2/utils/error-handler';
import { RolesRuntime } from '@/infrastructure/runtimes/roles.runtime';
import { effectValidator } from '@hono/effect-validator';
import { DbRoleSelectSchema } from '@rie/db-schema/entity-schemas';
import {
  CollectionViewParamSchema,
  ResourceIdSchema,
  RoleInputSchema,
  RoleListSchema,
  RoleSelectSchema,
} from '@rie/domain/schemas';
import type { HonoVariables } from '@rie/domain/types';
import { RolesServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

// Roles Routes
export const getAllRolesRoute = describeRoute({
  description: 'Obtient tous les rôles',
  operationId: 'getAllRoles',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: false,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue pour la collection (list ou select)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              Schema.Array(RoleListSchema),
              Schema.Array(RoleSelectSchema),
            ),
          ),
        },
      },
      description:
        'Rôles retournés avec succès. Le format dépend du paramètre "view": "list" retourne tous les champs (id, name, description, createdAt, updatedAt), "select" retourne le format value/label pour les dropdowns.',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

export const createRoleRoute = describeRoute({
  description: 'Crée un rôle',
  operationId: 'createRole',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(RoleInputSchema),
        example: {
          name: 'Equipment Manager',
          description: 'Manage equipment',
          directPermissionIds: ['equipment_read_01', 'equipment_write_01'],
          parentRoleIds: [],
          permissionGroupIds: ['equipment'],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(DbRoleSelectSchema),
        },
      },
      description: 'Role créée avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Role non trouvée',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Role déjà existant',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

export const updateRoleRoute = describeRoute({
  description: 'Mettre à jour un rôle',
  operationId: 'updateRole',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du role à mettre à jour',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(RoleInputSchema),
        example: {
          name: 'Equipment Editor',
          description: 'Can edit equipment information',
          directPermissionIds: ['equipment_update_01', 'equipment_read_01'],
          parentRoleIds: [],
          permissionGroupIds: ['equipment_management'],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(DbRoleSelectSchema),
        },
      },
      description: 'Role mis à jour avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Role non trouvée',
    },
    409: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Role déjà existant',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

export const deleteRoleRoute = describeRoute({
  description: 'Supprime un rôle',
  operationId: 'deleteRole',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID de la permission à supprimer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              success: Schema.Boolean,
              message: Schema.String,
            }),
          ),
        },
      },
      description: 'Rôle supprimée avec succès',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Input non valide',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Rôle non trouvée',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

// Create the router
const rolesRoute = new Hono<{
  Variables: HonoVariables;
}>();

// Get all roles
rolesRoute.get(
  '/',
  getAllRolesRoute,
  effectValidator('query', CollectionViewParamSchema),
  async (ctx) => {
    const { view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const rolesService = yield* RolesServiceLive;
      return yield* rolesService.getAllRoles({ view });
    });

    const roles = await RolesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, roles);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(roles)) {
      return ctx.json(roles.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Get role by ID route description
export const getRoleByIdRoute = describeRoute({
  description: 'Obtient un rôle par son ID',
  operationId: 'getRoleById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du rôle',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(DbRoleSelectSchema),
        },
      },
      description: 'Rôle retourné avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Rôle non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

// Get a role by ID
rolesRoute.get('/:id', getRoleByIdRoute, async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const rolesService = yield* RolesServiceLive;
    return yield* rolesService.getRoleById(id);
  });

  const maybeRole = await RolesRuntime.runPromiseExit(program);

  const errorResponse = handleEffectError(ctx, maybeRole);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(maybeRole)) {
    return ctx.json(maybeRole.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

// Create a new role
rolesRoute.post(
  '/',
  createRoleRoute,
  effectValidator('json', RoleInputSchema),
  async (ctx) => {
    const {
      name,
      description,
      directPermissionIds,
      parentRoleIds,
      permissionGroupIds,
    } = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const rolesService = yield* RolesServiceLive;
      return yield* rolesService.createRole({
        name,
        description,
        directPermissionIds,
        parentRoleIds,
        permissionGroupIds,
      });
    });

    const role = await RolesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, role);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(role)) {
      return ctx.json(role.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Update a role
rolesRoute.put(
  '/:id',
  updateRoleRoute,
  effectValidator('json', RoleInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');

    const program = Effect.gen(function* () {
      const rolesService = yield* RolesServiceLive;
      return yield* rolesService.updateRole({ id, ...body });
    });

    const role = await RolesRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, role);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(role)) {
      return ctx.json(role.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

// Delete a role
rolesRoute.delete('/:id', deleteRoleRoute, async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const rolesService = yield* RolesServiceLive;
    return yield* rolesService.deleteRole(id);
  });

  const result = await RolesRuntime.runPromiseExit(program);

  const errorResponse = handleEffectError(ctx, result);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(result)) {
    return ctx.json({
      success: true,
      message: 'Role deleted successfully',
    });
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

// Get role permissions route description
export const getRolePermissionsRoute = describeRoute({
  description: "Obtient toutes les permissions d'un rôle",
  operationId: 'getRolePermissions',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du rôle',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(Schema.Any)),
        },
      },
      description: 'Permissions du rôle retournées avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Rôle non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

// Get all permissions for a role
rolesRoute.get('/:id/permissions', getRolePermissionsRoute, async (ctx) => {
  const id = ctx.req.param('id');

  const program = Effect.gen(function* () {
    const rolesService = yield* RolesServiceLive;
    return yield* rolesService.getRolePermissions(id);
  });

  const permissions = await RolesRuntime.runPromiseExit(program);
  const errorResponse = handleEffectError(ctx, permissions);
  if (errorResponse) {
    return errorResponse;
  }

  if (Exit.isSuccess(permissions)) {
    return ctx.json(permissions.value);
  }

  return ctx.json({ error: 'An error occurred' }, 500);
});

// Get role permission groups route description
export const getRolePermissionGroupsRoute = describeRoute({
  description: "Obtient tous les groupes de permissions d'un rôle",
  operationId: 'getRolePermissionGroups',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du rôle',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(Schema.Any)),
        },
      },
      description: 'Groupes de permissions du rôle retournés avec succès',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Rôle non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne',
    },
  },
  tags: ['Rôles'],
});

// Get all permission groups for a role
rolesRoute.get(
  '/:id/permission-groups',
  getRolePermissionGroupsRoute,
  async (ctx) => {
    const id = ctx.req.param('id');

    const program = Effect.gen(function* () {
      const rolesService = yield* RolesServiceLive;
      return yield* rolesService.getRolePermissionGroups(id);
    });

    const permissionGroups = await RolesRuntime.runPromiseExit(program);

    const errorResponse = handleEffectError(ctx, permissionGroups);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(permissionGroups)) {
      return ctx.json(permissionGroups.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export default rolesRoute;
