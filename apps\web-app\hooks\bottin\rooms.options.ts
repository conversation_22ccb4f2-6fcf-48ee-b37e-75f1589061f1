import {
  getGenericByIdOptions,
  getGenericListOptions,
} from '@/hooks/bottin/generic-list.options';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllRoomsOptions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) =>
  getGenericListOptions({ controlledListKey: 'local', view } as {
    controlledListKey: 'local';
    view: View;
  });

export const getRoomByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) => getGenericByIdOptions({ controlledListKey: 'local', id, view } as const);
