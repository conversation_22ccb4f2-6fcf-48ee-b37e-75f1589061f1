import { handleEffectError } from '@/api/v2/utils/error-handler';
import { CampusesRuntime } from '@/infrastructure/runtimes/campuses.runtime';
import { effectValidator } from '@hono/effect-validator';
import {
  CampusInputSchema,
  CampusSchema,
  CollectionViewParamSchema,
  ResourceIdSchema,
} from '@rie/domain/schemas';
import {
  dbCampusToCampus,
  dbCampusesToCampuses,
} from '@rie/domain/serializers';
import type { HonoVariables } from '@rie/domain/types';
import { CampusesServiceLive } from '@rie/services';
import * as Effect from 'effect/Effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver } from 'hono-openapi/effect';

export const createCampusRoute = describeRoute({
  description: 'Créer un campus',
  operationId: 'createCampus',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CampusInputSchema),
        example: {
          sadId: 'NEW_CAMPUS_001',
          institutionId: 'sydsapwdh5p2m9z9tvnde2dp',
          translations: [
            {
              locale: 'en',
              name: 'New Main Campus',
            },
            {
              locale: 'fr',
              name: 'Nouveau Campus Principal',
            },
          ],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(CampusSchema),
        },
      },
      description: 'Campus créé',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Clé étrangère non trouvée - L'institution n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

export const updateCampusRoute = describeRoute({
  description: 'Mettre à jour un campus',
  operationId: 'updateCampus',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du campus (format CUID)',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(CampusInputSchema),
        example: {
          sadId: 'UPDATED_CAMPUS_001',
          institutionId: 'sydsapwdh5p2m9z9tvnde2dp',
          translations: [
            {
              locale: 'en',
              name: 'Updated Main Campus',
            },
            {
              locale: 'fr',
              name: 'Campus Principal Mis à Jour',
            },
          ],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(CampusSchema),
        },
      },
      description: 'Campus mis à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Erreur de validation - Données d'entrée invalides",
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: "Campus non trouvé ou l'institution n'existe pas",
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

export const deleteCampusRoute = describeRoute({
  description: 'Supprimer un campus',
  operationId: 'deleteCampus',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du campus (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Campus supprimé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Campus non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

export const getCampusByIdRoute = describeRoute({
  description: 'Obtenir un campus par ID',
  operationId: 'getCampusById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du campus (format CUID)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(CampusSchema),
        },
      },
      description: 'Campus trouvé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Campus non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

export const getAllCampusesRoute = describeRoute({
  description: 'Lister tous les campus',
  operationId: 'getAllCampuses',
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(Schema.Array(CampusSchema)),
        },
      },
      description: 'Campus retournés',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Campuses'],
});

const campusesRoute = new Hono<{
  Variables: HonoVariables;
}>();

campusesRoute.get(
  '/',
  getAllCampusesRoute,
  effectValidator('query', CollectionViewParamSchema),
  async (ctx) => {
    const { view = 'list' } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const campusService = yield* CampusesServiceLive;
      const campuses = yield* campusService.getAllCampuses();
      return dbCampusesToCampuses(campuses, view);
    });
    const result = await CampusesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

campusesRoute.get(
  '/:id',
  getCampusByIdRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const campusService = yield* CampusesServiceLive;
      const campus = yield* campusService.getCampusById(id);
      return dbCampusToCampus(campus, 'detail');
    });
    const result = await CampusesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

campusesRoute.post(
  '/',
  createCampusRoute,
  effectValidator('json', CampusInputSchema),
  async (ctx) => {
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const campusService = yield* CampusesServiceLive;
      return yield* campusService.createCampus({
        ...body,
        modifiedBy: user?.id,
      });
    });
    const result = await CampusesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value, 201);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

campusesRoute.put(
  '/:id',
  updateCampusRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  effectValidator('json', CampusInputSchema),
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = ctx.req.valid('json');
    const user = ctx.get('user');
    const program = Effect.gen(function* () {
      const campusService = yield* CampusesServiceLive;
      return yield* campusService.updateCampus({
        id,
        campus: {
          ...body,
          modifiedBy: user?.id,
        },
      });
    });
    const result = await CampusesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

campusesRoute.delete(
  '/:id',
  deleteCampusRoute,
  effectValidator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const program = Effect.gen(function* () {
      const campusService = yield* CampusesServiceLive;
      return yield* campusService.deleteCampus(id);
    });
    const result = await CampusesRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Campus deleted' });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { campusesRoute };
