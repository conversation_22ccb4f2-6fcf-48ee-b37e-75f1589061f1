import type { BuildingFormSchema } from '@/schemas/bottin/building-form-schema';
import type { CampusFormSchema } from '@/schemas/bottin/campus-form-schema';
import type { EstablishmentFormSchema } from '@/schemas/bottin/establishment-form-schema';
import type { FinancingProjectFormSchema } from '@/schemas/bottin/financing-project-form-schema';
import type { ManufacturerFormSchema } from '@/schemas/bottin/manufacturer-form-schema';
import type { PersonFormSchema } from '@/schemas/bottin/person-form-schema';
import type { RoomFormSchema } from '@/schemas/bottin/room-form-schema';
import type { UnitFormSchema } from '@/schemas/bottin/unit-form-schema';
import type { EstablishmentPostPayload } from '@/types/bottin/establishement';
import type {
  ManufacturerFull,
  ManufacturerPostPayload,
} from '@/types/bottin/manufacturer';
import type { PersonPostPayload } from '@/types/bottin/person';
import type { FundingProjectNumbers } from '@/types/bottin/project';
import type { RoomPostPayload } from '@/types/bottin/room';
import type { UnitPostPayload } from '@/types/bottin/unit';
import type {
  BuildingFull,
  BuildingPostPayload,
  CampusFull,
  CampusPostPayload,
  EstablishmentFull,
  RoomFull,
} from '@/types/building';
import type { EntityLocaleDictionary } from '@/types/common';
import type {
  PersonFull,
  ProviderFull,
  UnitFull,
} from '@/types/controlled-list';
import type { Equipment } from '@/types/equipment';
import type { DirectoryEntity } from '@rie/domain/types';

export type FundingProjectPostPayload = {
  anneeObtention: number;
  dateFin: string;
  descriptions: EntityLocaleDictionary;
  fciId: string;
  projetFinancementId: number;
  projetFinancementNumeros: {
    numero: string;
    projetFinancement?: {
      id: string;
    };
    typeNumero: {
      id: string;
    };
  }[];
  synchroId: string;
  titres: EntityLocaleDictionary;
  titulaire: {
    id: string;
  };
  typeProjet: {
    id: string;
  };
};

export type BaseEntity = {
  createdAt?: null | string;
  id: number | string;
  lastUpdatedAt?: null | string;
  text: string;
  uid: null | string;
};

export type Manufacturer = BaseEntity &
  Pick<ProviderFull, 'dateEnd' | 'emplacements' | 'telephones'>;

export type Building = {
  campus: string;
  jurisdiction?: null | string;
} & BaseEntity &
  Pick<BuildingFull, 'nom'>;

export type Campus = {
  jurisdiction: string;
} & BaseEntity;

export type Establishment = {
  establishmentType?: string;
} & BaseEntity &
  Pick<EstablishmentFull, 'acronym' | 'text'>;

export type FundingProject = {
  // infrastructure: string; //TODO: Ajouter lorsque ce sera recu dans le payload
  titreProjet: string;
  titulaire: string;
} & BaseEntity; //TODO

export type Location = {
  building: string;
  jurisdiction: string;
  numero: string;
} & BaseEntity;

export type Person = {
  email: string[];
  firstName: string;
  lastName: string;
} & BaseEntity;

export type Unit = {
  parentName?: string;
} & BaseEntity &
  Pick<UnitFull, 'acronym' | 'organizationId'>;

// Type union pour toutes les entités
export type Entity =
  | Building
  | Campus
  | Establishment
  | FundingProject
  | Location
  | Manufacturer
  | Person
  | Unit;

export type FundingProjectFull = {
  persons: Person[];
  equipements: Equipment[];
  infrastructures: BaseEntity[];
  anneeObtention: number;
  fciId: string;
  synchroId: string;
  titre: string;
  titres: EntityLocaleDictionary;
  titulaire: Person;
  dateFin: Date;
  descriptions: EntityLocaleDictionary;
  typeProjet: BaseEntity;
  projetFinancementNumeros: FundingProjectNumbers[];
} & BaseEntity;

// Type union pour toutes les entités Full
export type DirectoryFull =
  //TODO: Privilgier l'utilisation de EntityResponse et EntityPayload

  | BuildingFull
  | CampusFull
  | EstablishmentFull
  | FundingProjectFull
  | PersonFull
  | RoomFull
  | ProviderFull
  | UnitFull;

export type EntitySchemaFull =
  | EstablishmentFormSchema
  | ManufacturerFormSchema
  | CampusFormSchema
  | PersonFormSchema
  | FinancingProjectFormSchema
  | RoomFormSchema //TODO: Ajouter les projets de financement et autres manquants
  | UnitFormSchema
  | BuildingFormSchema;

export type EntityPostMap = {
  building: BuildingPostPayload;
  campus: CampusPostPayload;
  institution: EstablishmentPostPayload;
  fundingProject: FundingProjectPostPayload;
  vendor: ManufacturerPostPayload;
  people: PersonPostPayload;
  room: RoomPostPayload;
  unit: UnitPostPayload;
};

export type EntityPayload<K extends DirectoryEntity> = EntityPostMap[K];

export type EntityTypeMap = {
  building: Building;
  campus: Campus;
  institution: Establishment;
  fundingProject: FundingProject;
  vendor: Manufacturer;
  people: Person;
  room: Location;
  unit: Unit;
};

export type EntityResponseMap = {
  building: BuildingFull;
  campus: CampusFull;
  institution: EstablishmentFull;
  fundingProject: FundingProjectFull;
  vendor: ManufacturerFull;
  people: PersonFull;
  room: RoomFull;
  unit: UnitFull;
};
export type EntityResponse<K extends DirectoryEntity> = EntityResponseMap[K];
