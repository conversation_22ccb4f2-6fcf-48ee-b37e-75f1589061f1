import { EntityActions } from '@/components/entity-actions/entity-actions';
import { useDeleteInfrastructure } from '@/hooks/infrastructure/useDeleteInfrastructure';
import type { Infrastructure } from '@/types/infrastructure';
import type { Row } from '@tanstack/react-table';

type InfrastructureActionsProps = {
  row: Row<Infrastructure>;
};

export const InfrastructureActions = ({ row }: InfrastructureActionsProps) => {
  const { mutate: deleteInfrastructure } = useDeleteInfrastructure();

  return (
    <EntityActions
      row={row}
      config={{
        resource: 'infrastructure',
        basePath: '/infrastructures',
        deleteHook: deleteInfrastructure,
      }}
    />
  );
};
