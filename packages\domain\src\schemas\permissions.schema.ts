import { DbPermissionSelectSchema } from '@rie/db-schema/entity-schemas';
import { permissionActionEnum } from '@rie/db-schema/schemas';
import * as Schema from 'effect/Schema';
import { ResourceTypeSchema } from './user-permissions.schema';

export const PermissionActionSchema = Schema.Literal(
  ...permissionActionEnum.enumValues,
);

export const PermissionListSchema = DbPermissionSelectSchema;
export const PermissionDetailSchema = DbPermissionSelectSchema;

export const PermissionSelectSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

export const PermissionInputSchema = Schema.Struct({
  action: PermissionActionSchema,
  domain: ResourceTypeSchema,
});
