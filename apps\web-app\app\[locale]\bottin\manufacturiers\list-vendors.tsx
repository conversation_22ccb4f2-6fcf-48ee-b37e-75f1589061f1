'use client';

import { vendorsColumns } from '@/app/[locale]/bottin/manufacturiers/columns';
import { GenericList } from '@/components/bottin-table/generic-list';
import { initialColumnVisibility } from '@/constants/bottin/vendors';
import type { SupportedLocale } from '@/types/locale';
import type { VendorList } from '@rie/domain/types';

type VendorsListProps = {
  locale: SupportedLocale;
};

export const VendorsList = ({ locale }: VendorsListProps) => {
  return (
    <GenericList<'supplier', 'list', VendorList, Record<string, unknown>>
      controlledListKey="supplier"
      view="list"
      locale={locale}
      columns={vendorsColumns(locale)}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="vendors"
    />
  );
};
