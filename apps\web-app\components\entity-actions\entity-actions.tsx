import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { PermissionGate } from '@/components/permissions/permission-gate';
import { Link } from '@/lib/navigation';
import { Button } from '@/ui/button';
import type { Row } from '@tanstack/react-table';
import { useTranslations } from 'next-intl';
import { FaEdit, FaEye } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

type EntityBase = {
  id: string;
  name: string;
};

type EntityActionsConfig = {
  resource: 'equipment' | 'infrastructure';
  basePath: string; // e.g., '/equipements' or '/infrastructures'
  deleteHook: (id: string) => void;
};

type EntityActionsProps<T extends EntityBase> = {
  row: Row<T>;
  config: EntityActionsConfig;
};

export const EntityActions = <T extends EntityBase>({
  row,
  config,
}: EntityActionsProps<T>) => {
  const t = useTranslations('common');
  const { resource, basePath, deleteHook } = config;

  return (
    <div className="flex items-center justify-center gap-x-3">
      <PermissionGate
        action="update"
        resourceType={resource}
        resourceId={row.original.id}
      >
        <Button asChild size="icon">
          <Link
            data-testid={`edit-${resource}-${row.original.id}`}
            href={{
              params: { id: row.original.id },
              pathname: `${basePath}/[id]/editer` as
                | '/equipements/[id]/editer'
                | '/infrastructures/[id]/editer',
            }}
          >
            <FaEdit className="h-4 w-4" />
          </Link>
        </Button>
      </PermissionGate>
      <Button asChild size="icon" variant="outline">
        <Link
          data-testid={`view-${resource}-${row.original.id}`}
          href={{
            params: { id: row.original.id },
            pathname: `${basePath}/[id]` as
              | '/equipements/[id]'
              | '/infrastructures/[id]',
          }}
        >
          <FaEye className="h-4 w-4" />
        </Link>
      </Button>
      <PermissionGate
        action="delete"
        resourceType={resource}
        resourceId={row.original.id}
      >
        <DeleteConfirmation
          onDeleteAction={() => deleteHook(row.original.id)}
          title={t('deleteItemQuestion', { item: row.original.name })}
          trigger={
            <Button
              size="icon"
              variant="destructive"
              data-testid={`delete-${resource}-${row.original.id}`}
            >
              <MdDeleteForever className="h-4 w-4" />
            </Button>
          }
        />
      </PermissionGate>
    </div>
  );
};
