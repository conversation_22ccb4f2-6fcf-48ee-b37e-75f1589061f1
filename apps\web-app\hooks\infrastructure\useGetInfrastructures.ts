import { getAllInfrastructures } from '@/services/infrastructures.service';
import type { CollectionViewParamType } from '@rie/domain/types';
import { queryOptions, useQuery } from '@tanstack/react-query';

type InfraCollectionView = CollectionViewParamType['view'] | 'grid';

export const getInfrastructuresOptions = <View extends InfraCollectionView>({
  view,
}: { view: InfraCollectionView }) =>
  queryOptions({
    queryKey: ['v2', 'infrastructures', { view }],
    queryFn: () => getAllInfrastructures<View>({ view }),
  });

export const useGetInfrastructures = <View extends InfraCollectionView>({
  view,
}: { view: InfraCollectionView }) =>
  useQuery(getInfrastructuresOptions<View>({ view }));
