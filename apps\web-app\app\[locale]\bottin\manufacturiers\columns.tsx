'use client';

import { DeleteConfirmation } from '@/components/delete-confirmation/delete-confirmation';
import { Header<PERSON>enderer } from '@/components/header-renderer/header-renderer';
import { useDeleteVendor } from '@/hooks/bottin/vendors.hook';
import { Link } from '@/lib/navigation';
import type { SupportedLocale } from '@/types/locale';
import { Button } from '@/ui/button';
import type { VendorList } from '@rie/domain/types';
import type { ColumnDef, Row } from '@tanstack/react-table';
import { useFormatter, useTranslations } from 'next-intl';
import { FaEdit } from 'react-icons/fa';
import { MdDeleteForever } from 'react-icons/md';

export const vendorsColumns = (
  _locale: SupportedLocale,
): ColumnDef<VendorList>[] => {
  const directoryEntity = 'vendor' as const;

  return [
    {
      accessorKey: 'text',
      cell: (cell) => cell.getValue(),
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.name"
        />
      ),
      id: 'name',
      maxSize: 400,
      minSize: 250,
    },
    {
      accessorKey: 'dateEnd',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = cell.getValue() as string | null;
        const formattedDate = date
          ? format.dateTime(new Date(date), {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.dateEnd"
        />
      ),
      id: 'dateEnd',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'lastUpdatedAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'lastUpdatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => <VendorActions row={row} />,
      id: 'actions',
      size: 105,
    },
  ];
};

const VendorActions = ({ row }: { row: Row<VendorList> }) => {
  const tCommon = useTranslations('common');
  const { mutate: deleteVendor } = useDeleteVendor();

  return (
    <div className="flex items-center justify-center gap-x-3">
      <Button asChild size="icon">
        <Link
          data-testid="edit-vendor"
          href={{
            params: { id: row.original.id },
            pathname: '/bottin/manufacturiers/[id]/editer',
          }}
        >
          <FaEdit className="h-4 w-4" />
        </Link>
      </Button>
      <DeleteConfirmation
        onDeleteAction={() => {
          deleteVendor(row.original.id);
        }}
        title={tCommon('deleteItemQuestion', { item: row.original.text })}
        trigger={
          <Button size="icon" variant="destructive" data-testid="delete-vendor">
            <MdDeleteForever className="h-4 w-4" />
          </Button>
        }
      />
    </div>
  );
};
