{"name": "web-app", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS='--inspect' next dev --turbopack", "build": "next build", "start": "next start", "lint": "pnpm biome lint --write", "format": "pnpm biome format --write", "test": "vitest", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^4.1.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@rie/auth": "workspace:*", "@rie/constants": "workspace:*", "@rie/domain": "workspace:*", "@t3-oss/env-nextjs": "^0.10.1", "@tanstack/react-query": "^5.66.9", "@tanstack/react-query-devtools": "^5.66.9", "@tanstack/react-table": "^8.21.2", "@uidotdev/usehooks": "^2.4.1", "accept-language": "^3.0.18", "axios": "^1.7.2", "better-auth": "^1.2.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookies-next": "^4.2.1", "date-fns": "^4.1.0", "dayjs": "^1.11.11", "effect": "^3.16.9", "jiti": "^1.21.6", "ky": "^1.8.1", "lightgallery": "^2.8.3", "lucide-react": "^0.479.0", "next": "15.3.4", "next-intl": "^4.3.1", "next-themes": "^0.4.4", "nuqs": "^2.4.3", "oslo": "1.2.0", "pg": "^8.16.2", "posthog-js": "^1.223.4", "react": "19.1.0", "react-day-picker": "^9.7.0", "react-dom": "19.1.0", "react-hook-form": "^7.52.0", "react-icons": "^5.2.1", "react-player": "^2.16.0", "remeda": "^2.23.1", "sonner": "^2.0.5", "tailwind-merge": "^2.3.0", "tailwindcss-animate": "^1.0.7", "vite-tsconfig-paths": "^4.3.2", "zod": "^3.24.2", "zustand": "^4.5.4"}, "devDependencies": {"@hookform/devtools": "^4.3.3", "@next/env": "15.2.4", "@rie/biome-config": "workspace:*", "@rie/db-schema": "workspace:*", "@rie/typescript-config": "workspace:*", "@rie/utils": "workspace:*", "@storybook/addon-essentials": "^8.6.0", "@storybook/addon-interactions": "^8.6.0", "@storybook/addon-links": "^8.6.0", "@storybook/addon-onboarding": "^8.6.0", "@storybook/blocks": "^8.6.0", "@storybook/nextjs": "^8.6.0", "@storybook/react": "^8.6.0", "@storybook/test": "^8.6.0", "@storybook/testing-library": "^0.2.2", "@tailwindcss/typography": "^0.5.13", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^24.0.3", "@types/react": "19.0.10", "@types/react-day-picker": "^5.3.0", "@types/react-dom": "19.0.4", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "msw": "^2.3.1", "postcss": "^8", "storybook": "^8.6.0", "tailwindcss": "^3.4.4", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "msw": {"workerDirectory": ["public"]}}