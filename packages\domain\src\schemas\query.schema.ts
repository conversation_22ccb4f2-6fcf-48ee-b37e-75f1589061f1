import { isCuid } from '@paralleldrive/cuid2';
import * as Schema from 'effect/Schema';

export const LocaleSchema = Schema.Union(
  Schema.Literal('fr'),
  Schema.Literal('en'),
);

export const LocaleQuerySchema = Schema.Struct({
  locale: LocaleSchema,
});

export const CollectionViewSchema = Schema.Union(
  Schema.Literal('list'),
  Schema.Literal('grid'),
  Schema.Literal('select'),
);

export const CollectionViewParamSchema = Schema.Struct({
  view: CollectionViewSchema,
});

// Single resource view types
export const ResourceViewSchema = Schema.Union(
  Schema.Literal('detail'),
  Schema.Literal('edit'),
);

export const ResourceViewParamSchema = Schema.Struct({
  view: ResourceViewSchema,
});

export const SortSchema = Schema.String.pipe(
  Schema.pattern(/^\[[^\]]+\]=(asc|desc)$/, {
    message: () => 'sort must be in format: [columnName]=(asc|desc)',
  }),
);

export const SortQuerySchema = Schema.Struct({
  sort: Schema.optional(SortSchema),
});

export const PageParamSchema = Schema.NumberFromString.pipe(
  Schema.greaterThan(0, { message: () => 'page must be greater than 0' }),
);

export const LimitParamSchema = Schema.NumberFromString.pipe(
  Schema.greaterThanOrEqualTo(-1, {
    message: () => "limit can't be less than -1 ",
  }),
);

export const PaginationQuerySchema = Schema.Struct({
  page: Schema.optional(PageParamSchema),
  limit: Schema.optional(LimitParamSchema),
});

// Schema for collection endpoints (GET /units)
export const CollectionQuerySchema = Schema.Struct({
  ...CollectionViewParamSchema.fields,
  ...LocaleQuerySchema.fields,
  ...SortQuerySchema.fields,
  ...PaginationQuerySchema.fields,
});

// Schema for single resource endpoints (GET /units/:id)
export const ResourceQuerySchema = Schema.Union(
  Schema.Struct({
    view: Schema.Literal('detail'),
    ...LocaleQuerySchema.fields,
  }),
  Schema.Struct({
    view: Schema.Literal('edit'),
    locale: Schema.optional(LocaleSchema),
  }),
);

export const ResourceIdSchema = Schema.String.pipe(
  Schema.filter((id) => isCuid(id), {
    message: () => 'Invalid id format: must be a valid CUID2',
    identifier: 'ResourceId',
  }),
);
