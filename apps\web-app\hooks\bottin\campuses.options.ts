import {
  getGenericByIdOptions,
  getGenericListOptions,
} from '@/hooks/bottin/generic-list.options';
import type {
  CollectionViewParamType,
  ResourceViewType,
} from '@rie/domain/types';

export const getAllCampusesOptions = <
  View extends CollectionViewParamType['view'],
>({
  view,
}: CollectionViewParamType) =>
  getGenericListOptions({ controlledListKey: 'campus', view } as {
    controlledListKey: 'campus';
    view: View;
  });

export const getCampusByIdOptions = <View extends ResourceViewType>({
  id,
  view,
}: {
  id: string;
  view: View;
}) => getGenericByIdOptions({ controlledListKey: 'campus', id, view } as const);
