import { BuildingNotFoundError } from '@rie/domain/errors';
import type { BuildingFormSchema, BuildingInputSchema } from '@rie/domain/schemas';
import { BuildingInputToDBInput } from '@rie/domain/serializers';
import type { DBBuildingInput } from '@rie/domain/types';
import { BuildingsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import * as Schema from 'effect/Schema';

type BuildingForm = Schema.Schema.Type<typeof BuildingFormSchema>;
type BuildingInput = Schema.Schema.Type<typeof BuildingInputSchema>;

export class BuildingsServiceLive extends Effect.Service<BuildingsServiceLive>()(
  'BuildingsServiceLive',
  {
    dependencies: [BuildingsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllBuildings = () => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const all = yield* repo.findAllBuildings();
          const t1 = performance.now();
          console.log(`Call to getAllBuildings took ${t1 - t0} ms.`);
          return all;
        });
      };

      const getBuildingById = (id: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const building = yield* repo.findBuildingById(id);
          if (!building) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const t1 = performance.now();
          console.log(`Call to getBuildingById took ${t1 - t0} ms.`);
          return building;
        });
      };
      // Create from form payload
      const createBuildingFromForm = ({
        payload,
        modifiedBy,
      }: {
        payload: BuildingForm;
        modifiedBy?: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const building = yield* Schema.decode(BuildingInputToDBInput)(payload);
          const buildingWithMetadata = modifiedBy
            ? { ...building, modifiedBy }
            : building;
          return yield* repo.createBuilding({ building: buildingWithMetadata });
        });

      // Create with DB-ready input
      const createBuilding = (building: BuildingInput) =>
        Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const dbBuilding: DBBuildingInput = yield* Schema.decode(
            BuildingInputToDBInput,
          )(building);
          return yield* repo.createBuilding({ building: dbBuilding });
        });

      // Update from form payload
      const updateBuildingFromForm = ({
        id,
        payload,
        modifiedBy,
      }: {
        payload: BuildingForm;
        id: string;
        modifiedBy?: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const existingBuilding = yield* repo.findBuildingById(id);
          if (!existingBuilding) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const building = yield* Schema.decode(BuildingInputToDBInput)(payload);
          const buildingWithMetadata = modifiedBy
            ? { ...building, modifiedBy }
            : building;
          return yield* repo.updateBuilding({
            buildingId: id,
            building: buildingWithMetadata,
          });
        });

      // Update with DB-ready input
      const updateBuilding = ({
        id,
        building,
      }: {
        building: BuildingInput;
        id: string;
      }) =>
        Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const existingBuilding = yield* repo.findBuildingById(id);
          if (!existingBuilding) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const dbBuilding: DBBuildingInput = yield* Schema.decode(
            BuildingInputToDBInput,
          )(building);
          return yield* repo.updateBuilding({
            buildingId: id,
            building: dbBuilding,
          });
        });

      const deleteBuilding = (id: string) => {
        const t0 = performance.now();
        return Effect.gen(function* () {
          const repo = yield* BuildingsRepositoryLive;
          const existingBuilding = yield* repo.findBuildingById(id);
          if (!existingBuilding) {
            return yield* Effect.fail(new BuildingNotFoundError({ id }));
          }
          const result = yield* repo.deleteBuilding(id);
          const t1 = performance.now();
          console.log(`Call to deleteBuilding took ${t1 - t0} ms.`);
          return result.length > 0;
        });
      };

      return {
        getAllBuildings,
        getBuildingById,
        createBuildingFromForm,
        createBuilding,
        updateBuildingFromForm,
        updateBuilding,
        deleteBuilding,
      } as const;
    }),
  },
) { }
