import { PermissionsGroupsList } from '@/app/[locale]/admin/groupes-permissions/list-permissions-groups';
import { getQueryClientOptions } from '@/constants/query-client';
import { getAllPermissionsGroupsOptions } from '@/hooks/admin/permissions/permissions-groups.options';
import { redirect } from '@/lib/navigation';
import type { BasePageParams } from '@/types/common';
import { auth } from '@rie/auth';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { headers } from 'next/headers';

export default async function PermissionsPage({ params }: BasePageParams) {
  const { locale } = await params;

  const sessionData = await auth.api.getSession({ headers: await headers() });

  if (!sessionData?.user) {
    return redirect({
      href: {
        pathname: '/login',
        query: { from: '/admin/groupes-permissions' },
      },
      locale,
    });
  }

  const queryClient = new QueryClient(getQueryClientOptions(locale));
  await queryClient.prefetchQuery(
    getAllPermissionsGroupsOptions({ view: 'list' }),
  );

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <PermissionsGroupsList locale={locale} />
    </HydrationBoundary>
  );
}
