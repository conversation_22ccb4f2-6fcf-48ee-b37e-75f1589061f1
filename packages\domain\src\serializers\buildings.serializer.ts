import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import {
  BuildingEditSchema,
  BuildingFormSchema,
  BuildingInputSchema,
  BuildingListSchema,
  BuildingSelectSchema,
  DBBuildingInputSchema,
  DbBuildingSchema,
} from '../schemas';
import type {
  BuildingList,
  BuildingSelect,
  CollectionViewType,
  DbBuilding,
  ResourceViewType,
} from '../types';

// Schema transformer for converting database building to list view
export const DbBuildingToBuildingList = Schema.transformOrFail(
  DbBuildingSchema,
  BuildingListSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation (could be 'fr' or 'en')
          const defaultTranslation =
            raw.translations.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations[0];

          // Get campus object with id and text from translations
          const campus = raw.campus
            ? {
              id: raw.campus.id,
              text:
                (
                  raw.campus.translations?.find((t) => t.locale === 'fr') ||
                  raw.campus.translations?.find((t) => t.locale === 'en') ||
                  raw.campus.translations?.[0]
                )?.name || raw.campus.id,
            }
            : null;

          return {
            id: raw.id,
            civicAddressId: raw.civicAddressId,
            sadId: raw.sadId,
            diId: raw.diId,
            createdAt: raw.createdAt,
            updatedAt: raw.updatedAt,
            modifiedBy: raw.modifiedBy,
            name: defaultTranslation?.name || null,
            description: defaultTranslation?.description || null,
            campus: campus,
            otherNames: defaultTranslation?.otherNames || null,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse building for list view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database building to select view
export const DbBuildingToBuildingSelect = Schema.transformOrFail(
  DbBuildingSchema,
  BuildingSelectSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Get the default locale translation for label
          const defaultTranslation =
            raw.translations.find(
              (translation) => translation.locale === 'fr',
            ) ||
            raw.translations.find(
              (translation) => translation.locale === 'en',
            ) ||
            raw.translations[0];

          return {
            value: raw.id,
            label: defaultTranslation?.name || raw.id,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse building for select view',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting database building to edit format
export const DbBuildingToBuildingEdit = Schema.transformOrFail(
  DbBuildingSchema,
  BuildingEditSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          return {
            id: raw.id,
            campusId: raw.campusId,
            civicAddressId: raw.civicAddressId,
            sadId: raw.sadId,
            diId: raw.diId,
            translations: raw.translations.map((translation) => ({
              locale: translation.locale as 'fr' | 'en',
              name: translation.name || '',
              description: translation.description || undefined,
              otherNames: translation.otherNames || undefined,
            })),
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to parse building for edit format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting input format to database input format
export const BuildingInputToDBInput = Schema.transformOrFail(
  BuildingInputSchema,
  DBBuildingInputSchema,
  {
    strict: true,
    decode: (val, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Map form schema to BuildingInput
          const translations = val.name.map((nameItem) => {
            const aliasItem = val.alias.find(
              (a) => a.locale === nameItem.locale,
            );
            return {
              locale: nameItem.locale as 'fr' | 'en',
              name: nameItem.value,
              description: undefined,
              otherNames: aliasItem?.value,
            };
          });

          return {
            campusId: val.campus?.value || null,
            civicAddressId: null,
            sadId: null,
            diId: null,
            translations,
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            val,
            error instanceof Error
              ? error.message
              : 'Failed to convert input format to database input format',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting BuildingEdit to form schema
export const BuildingEditToFormSchema = Schema.transformOrFail(
  BuildingEditSchema,
  BuildingFormSchema,
  {
    strict: true,
    decode: (raw, _options, ast) =>
      ParseResult.try({
        try: () => {
          // Map translations to name and alias
          const name = raw.translations.map((translation) => ({
            locale: translation.locale,
            value: translation.name || '',
          }));

          const alias = raw.translations
            .filter((translation) => translation.otherNames)
            .map((translation) => ({
              locale: translation.locale,
              value: translation.otherNames || '',
            }));

          return {
            id: raw.id,
            name,
            alias,
            campus: raw.campusId
              ? { label: raw.campusId, value: raw.campusId }
              : { label: null, value: null },
            jurisdiction: { label: null, value: null },
            civicAddresses: [],
          };
        },
        catch(error) {
          return new ParseResult.Type(
            ast,
            raw,
            error instanceof Error
              ? error.message
              : 'Failed to convert BuildingEdit to form schema',
          );
        },
      }),
    encode: (val, _options, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
      ),
  },
);

// Schema transformer for converting form schema to BuildingInput (no longer needed as BuildingInputSchema = BuildingFormSchema)
export const BuildingFormToInputSchema = BuildingFormSchema;

// Alias for BuildingInputToDBInput to match the pattern used in vendors
export const BuildingFormToDBInput = BuildingInputToDBInput;

// Helper function to transform an array of DbBuildings to BuildingSelect[]
export const dbBuildingsToBuildingSelect = (
  dbBuildings: DbBuilding[],
): BuildingSelect[] => {
  return dbBuildings.map((dbBuilding) => {
    const defaultTranslation =
      dbBuilding.translations.find(
        (translation) => translation.locale === 'fr',
      ) ||
      dbBuilding.translations.find(
        (translation) => translation.locale === 'en',
      ) ||
      dbBuilding.translations[0];

    return {
      value: dbBuilding.id,
      label: defaultTranslation?.name || dbBuilding.id,
    };
  });
};

// Main serializer function that takes view parameter and returns appropriate transformation
export const dbBuildingsToBuildings = (
  dbBuildings: DbBuilding[],
  view: CollectionViewType,
): BuildingList[] | BuildingSelect[] => {
  return view === 'select'
    ? dbBuildings.map((building) =>
      Schema.decodeUnknownSync(DbBuildingToBuildingSelect)(building),
    )
    : dbBuildings.map((building) =>
      Schema.decodeUnknownSync(DbBuildingToBuildingList)(building),
    );
};

// New serializer function for DbBuildingSchema with view parameter
export const dbBuildingToBuilding = (
  dbBuilding: DbBuilding,
  view: ResourceViewType,
) => {
  return view === 'edit'
    ? Schema.decodeUnknownSync(DbBuildingToBuildingEdit)(dbBuilding)
    : Schema.decodeUnknownSync(DbBuildingToBuildingList)(dbBuilding);
};
