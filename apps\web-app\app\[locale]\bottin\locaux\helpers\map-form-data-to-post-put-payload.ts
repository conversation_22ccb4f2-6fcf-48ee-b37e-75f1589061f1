import type { RoomFormSchema } from '@/schemas/bottin/room-form-schema';
import type { EntityPayload } from '@/types/bottin/directory';
import type { RoomPostPayload } from '@/types/bottin/room';

export const mapRoomBaseForm = (formData: RoomFormSchema): RoomPostPayload => ({
  batiment: { id: formData.building?.value ?? '' },
  categories: formData.categories.map((category) => ({
    id: category.value,
  })),
  juridiction: { id: formData.jurisdiction?.value ?? '' },
  numero: formData.roomNumber,
  portantePlancher: formData.capacity.toString(),
  pseudonyme: formData.alias,
  superficie: formData.area.toString(),
});

export const mapFormDataToPostPutPayload = (
  formData: RoomFormSchema,
): EntityPayload<'room'> => {
  return mapRoomBaseForm(formData);
};
