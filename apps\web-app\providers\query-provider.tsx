'use client';

import { getQueryClientOptions } from '@/constants/query-client';
import { useAvailableLocale } from '@/hooks/useAvailableLocale';
import type { SupportedLocale } from '@/types/locale';
// We can not useState or useRef in a server component, which is why we are
// extracting this part out into its own file with 'use client' on top
import {
  QueryClient,
  type QueryClientConfig,
  QueryClientProvider,
  isServer,
} from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import type { PropsWithChildren } from 'react';

function makeQueryClient(
  locale: SupportedLocale,
  queryOptions?: Partial<QueryClientConfig>,
) {
  return new QueryClient({ ...getQueryClientOptions(locale), ...queryOptions });
}
let browserQueryClient: QueryClient | undefined = undefined;

function getQueryClient(
  locale: SupportedLocale,
  queryOptions?: Partial<QueryClientConfig>,
) {
  if (isServer) {
    // Server: always make a new query client
    return makeQueryClient(locale, queryOptions);
  }
  // Browser: make a new query client if we don't already have one
  // This is very important, so we don't re-make a new client if React
  // suspends during the initial render. This may not be needed if we
  // have a suspense boundary BELOW the creation of the query client
  if (!browserQueryClient) {
    browserQueryClient = makeQueryClient(locale, queryOptions);
  }
  return browserQueryClient;
}

type QueryProviderProps = {
  queryOptions?: Partial<QueryClientConfig>;
};
export default function QueryProvider({
  children,
  queryOptions,
}: PropsWithChildren<QueryProviderProps>) {
  const locale = useAvailableLocale();
  const queryClient = getQueryClient(locale, queryOptions);

  return (
    <QueryClientProvider client={queryClient}>
      <ReactQueryDevtools />
      {children}
    </QueryClientProvider>
  );
}
