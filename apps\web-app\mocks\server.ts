import { authHandlers } from './handlers/auth.handler';
import { infrastructuresHandlers } from '@/mocks/handlers/infrastructures';
import { peopleHandlers } from '@/mocks/handlers/people';
import { functionalitiesHandlers } from 'mocks/handlers/functionality.handler';
import { setupServer } from 'msw/node';

export const server = setupServer(
  ...authHandlers,
  ...infrastructuresHandlers,
  ...peopleHandlers,
  ...functionalitiesHandlers,
);
